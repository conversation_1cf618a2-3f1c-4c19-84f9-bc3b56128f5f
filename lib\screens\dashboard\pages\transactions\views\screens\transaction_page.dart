// Unified Transaction Page
// Handles all transaction types (user, kitty, chama, event) with unified interface

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/utils/utils_exports.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/widgets/export_widget.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';

import '../../models/transaction_type.dart';
import '../../controllers/transaction_controller.dart';
import '../widgets/enhanced_transaction_filter.dart';
import '../widgets/unified_transaction_item.dart';
import '../../utils/filter_error_handler.dart';


class TransactionPage extends StatefulWidget {
  final TransactionPageConfig config;

  const TransactionPage({
    super.key,
    required this.config,
  });

  @override
  State<TransactionPage> createState() => _TransactionPageState();
}

class _TransactionPageState extends State<TransactionPage> {
  late final TransactionController _controller;
  final ScrollController _scrollController = ScrollController();
  final DateFormat _dateFormat = DateFormat('EE, dd MMMM');

  @override
  void initState() {
    super.initState();
    _controller = Get.put(TransactionController(), tag: widget.config.transactionType.name);
    _controller.initialize(widget.config);
    _setupScrollListener();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >= 
          _scrollController.position.maxScrollExtent - 200) {
        _controller.loadMoreTransactions();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    Get.delete<TransactionController>(tag: widget.config.transactionType.name);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // If not full page, render as embedded content without Scaffold
    if (!widget.config.isFullPage) {
      return _buildEmbeddedContent();
    }

    // Full page view with Scaffold
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          _buildFilterStatusIndicator(),
          Expanded(child: _buildTransactionsList()),
        ],
      ),
    );
  }

  /// Build embedded content for non-full-page views
  Widget _buildEmbeddedContent() {
    return Column(
      children: [
        _buildEmbeddedHeader(),
        _buildSearchAndFilters(),
        _buildFilterStatusIndicator(),
        Expanded(child: _buildTransactionsList()),
      ],
    );
  }

  /// Build header for embedded view with "See All" button
  Widget _buildEmbeddedHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            widget.config.title ?? widget.config.transactionType.displayName,
            style: const TextStyle(
              color: Colors.black87,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          TextButton.icon(
            style: TextButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: EdgeInsets.symmetric(
                horizontal: 12.w,
                vertical: 6.h,
              ),
            ),
            onPressed: _navigateToFullView,
            icon: Icon(
              Icons.open_in_new_rounded,
              size: 16.h,
              color: AppColors.primary,
            ),
            label: Text(
              "See all",
              style: TextStyle(
                fontSize: 12.sp,
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Navigate to full transaction view
  void _navigateToFullView() {
    final fullConfig = widget.config.copyWith(isFullPage: true);
    Get.to(() => TransactionPage(config: fullConfig));
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios, color: AppColors.dark),
        onPressed: () => Get.back(),
      ),
      title: Text(
        widget.config.title ?? widget.config.transactionType.displayName,
        style: const TextStyle(
          color: Colors.black87,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        if (widget.config.showExportOptions)
          IconButton(
            icon: const Icon(Icons.file_download_outlined, color: AppColors.dark),
            onPressed: _showExportOptions,
          ),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    return EnhancedTransactionFilter(
      transactionType: widget.config.transactionType,
      controllerTag: widget.config.transactionType.name,
    );
  }

  /// Build filter status indicator
  Widget _buildFilterStatusIndicator() {
    return Obx(() {
      if (!_controller.hasActiveFilters) {
        return const SizedBox.shrink();
      }

      return Container(
        margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: AppColors.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: AppColors.primary.withOpacity(0.3),
            width: 1.w,
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.filter_alt_rounded,
              size: 16.h,
              color: AppColors.primary,
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Text(
                _getFilterStatusText(),
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColors.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            TextButton(
              onPressed: _controller.clearAllFilters,
              style: TextButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: Text(
                'Clear',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColors.primary,
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  /// Get filter status text
  String _getFilterStatusText() {
    final filters = <String>[];

    if (_controller.searchQuery.isNotEmpty) {
      filters.add('Search: "${_controller.searchQuery}"');
    }

    if (_controller.phoneNumber.isNotEmpty) {
      filters.add('Phone: ${_controller.phoneNumber}');
    }

    if (_controller.transactionCode.isNotEmpty) {
      filters.add('Code: ${_controller.transactionCode}');
    }

    if (_controller.startDate != null && _controller.endDate != null) {
      final startDate = DateFormat('MMM dd').format(_controller.startDate!);
      final endDate = DateFormat('MMM dd').format(_controller.endDate!);
      filters.add('Date: $startDate - $endDate');
    }

    if (filters.isEmpty) {
      return 'Filters active';
    }

    return filters.join(' • ');
  }

  Widget _buildTransactionsList() {
    return Obx(() {
      // Show filter loading indicator
      if (_controller.isFilterLoading) {
        return const FilterLoadingIndicator(
          message: 'Applying filters...',
          isVisible: true,
        );
      }

      // Show error state
      if (_controller.hasError && _controller.transactions.isEmpty) {
        return FilterErrorDisplay(
          error: _controller.lastError!,
          onRetry: _controller.retryLastOperation,
        );
      }

      // Show loading indicator for initial load
      if (_controller.isLoading && _controller.transactions.isEmpty) {
        return _buildLoadingIndicator();
      }

      // Show empty state
      if (_controller.isEmpty) {
        return _buildEmptyState();
      }

      return RefreshIndicator(
        onRefresh: _controller.refreshTransactions,
        child: _buildGroupedTransactionsList(),
      );
    });
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: SpinKitFadingCircle(
        color: AppColors.primary,
        size: 50.0,
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 64.w,
            color: AppColors.neutralGrey,
          ),
          SizedBox(height: 16.h),
          Text(
            widget.config.transactionType == TransactionType.kitty
              ? 'You have no transactions yet'
              : 'No transactions found',
            style: const TextStyle(
              color: Colors.black54,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),
          const Text(
            'Transactions will appear here once available',
            style: TextStyle(
              color: Colors.black54,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
          // Show contribute button for kitty transactions
          if (widget.config.transactionType == TransactionType.kitty) ...[
            SizedBox(height: 16.h),
            _buildContributeButton(),
          ],
        ],
      ),
    );
  }

  Widget _buildContributeButton() {
    final contributeController = Get.find<ContributeController>();
    final dataController = Get.find<DataController>();

    return Obx(() => CustomKtButton(
      isLoading: contributeController.isgetkittyloading.isTrue,
      onPress: () async {
        final id = widget.config.entityId;
        final res = await contributeController.getKitty(id: id);
        if (res) {
          Get.toNamed(NavRoutes.kittycontributionScreen + id.toString());
        }
      },
      width: 110.w,
      height: 23.h,
      btnText: "Contribute",
    ));
  }

  Widget _buildGroupedTransactionsList() {
    return GroupedListView<TransactionModel, String>(
      controller: _scrollController,
      elements: _controller.filteredTransactions,
      groupBy: (transaction) => _dateFormat.format(transaction.createdAt ?? DateTime.now()),
      groupSeparatorBuilder: (String groupByValue) => _buildDateHeader(groupByValue),
      itemBuilder: (context, TransactionModel transaction) => UnifiedTransactionItem(
        transaction: transaction,
        transactionType: widget.config.transactionType,
        onTap: () => _handleTransactionTap(transaction),
        showEditOption: widget.config.showEditOptions &&
                       widget.config.transactionType.supportsEdit,
      ),
      itemComparator: (item1, item2) => 
          (item2.createdAt ?? DateTime.now()).compareTo(item1.createdAt ?? DateTime.now()),
      useStickyGroupSeparators: true,
      floatingHeader: true,
      order: GroupedListOrder.ASC,
      footer: _buildFooter(),
    );
  }

  Widget _buildDateHeader(String date) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      color: AppColors.background,
      child: Row(
        children: [
          Icon(Icons.calendar_today, size: 16.w, color: AppColors.neutralGrey),
          SizedBox(width: 8.w),
          Text(
            date,
            style: const TextStyle(
              color: Colors.black54,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Obx(() {
      if (_controller.isLoadingMore) {
        return Container(
          padding: EdgeInsets.all(16.w),
          child: const Center(
            child: SpinKitThreeBounce(
              color: AppColors.primary,
              size: 20.0,
            ),
          ),
        );
      }
      
      if (!_controller.hasMoreData && _controller.transactions.isNotEmpty) {
        return Container(
          padding: EdgeInsets.all(16.w),
          child: const Center(
            child: Text(
              'No more transactions',
              style: TextStyle(
                color: Colors.black54,
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        );
      }
      
      return const SizedBox.shrink();
    });
  }

  void _handleTransactionTap(TransactionModel transaction) {
    // Handle transaction tap based on type
    switch (widget.config.transactionType) {
      case TransactionType.kitty:
        _showTransactionDetails(transaction);
        break;
      case TransactionType.chama:
        _showChamaTransactionDetails(transaction);
        break;
      case TransactionType.event:
        _showEventTransactionDetails(transaction);
        break;
      case TransactionType.user:
        _showUserTransactionDetails(transaction);
        break;
    }
  }

  void _showTransactionDetails(TransactionModel transaction) {
    // Show transaction details modal or navigate to details page
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildTransactionDetailsSheet(transaction),
    );
  }

  void _showChamaTransactionDetails(TransactionModel transaction) {
    // Implement chama-specific transaction details
    _showTransactionDetails(transaction);
  }

  void _showEventTransactionDetails(TransactionModel transaction) {
    // Implement event-specific transaction details
    _showTransactionDetails(transaction);
  }

  void _showUserTransactionDetails(TransactionModel transaction) {
    // Implement user-specific transaction details
    _showTransactionDetails(transaction);
  }

  Widget _buildTransactionDetailsSheet(TransactionModel transaction) {
    return DraggableScrollableSheet(
      initialChildSize: 0.7,
      maxChildSize: 0.9,
      minChildSize: 0.5,
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
          ),
          child: Column(
            children: [
              Container(
                width: 40.w,
                height: 4.h,
                margin: EdgeInsets.symmetric(vertical: 12.h),
                decoration: BoxDecoration(
                  color: AppColors.neutral,
                  borderRadius: BorderRadius.circular(2.r),
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: EdgeInsets.all(16.w),
                  child: _buildTransactionDetailsContent(transaction),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTransactionDetailsContent(TransactionModel transaction) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Transaction Details',
          style: CustomTextStyles.titleLargeGray900,
        ),
        SizedBox(height: 20.h),
        // Add transaction details here
        _buildDetailRow('Amount', 'KES ${transaction.amount?.toStringAsFixed(2) ?? '0.00'}'),
        _buildDetailRow('Reference', transaction.transactionRef ?? 'N/A'),
        _buildDetailRow('Status', transaction.status ?? 'N/A'),
        _buildDetailRow('Date', DateFormat('dd MMM yyyy, hh:mm a').format(transaction.createdAt ?? DateTime.now())),
        if (transaction.firstName != null || transaction.secondName != null)
          _buildDetailRow('Name', '${transaction.firstName ?? ''} ${transaction.secondName ?? ''}'),
        if (transaction.phoneNumber != null)
          _buildDetailRow('Phone', transaction.phoneNumber!),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100.w,
            child: Text(
              label,
              style: CustomTextStyles.bodyMediumGray600,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.black87,
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showExportOptions() {
    // Show export options based on transaction type
    showModalBottomSheet(
      context: context,
      builder: (context) => ExportContentWidget(
        singleTrans: false,
        // Add other required parameters based on transaction type
      ),
    );
  }
}
