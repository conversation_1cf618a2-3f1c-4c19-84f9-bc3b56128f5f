// Unified Transaction Service
// Handles all transaction-related API calls across different transaction types

import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/models/user_transaction_model.dart';
import 'package:onekitty/models/chama/chama_transactions.dart' as chama;
import 'package:onekitty/models/events/signatory_transaction_model.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/services/api_urls.dart';
import '../models/transaction_type.dart';

class TransactionService extends GetxService {
  final HttpService _apiProvider = Get.find<HttpService>();
  final Logger _logger = Get.find<Logger>();

  /// Fetch transactions based on type and parameters
  Future<List<TransactionModel>> fetchTransactions({
    required TransactionType type,
    int? entityId,
    String? userId,
    String? phoneNumber,
    int page = 0,
    int size = 20,
    String? accountNo,
  }) async {
    try {
      String url = _buildUrl(type, entityId, userId, phoneNumber, page, size, accountNo);
      _logger.i('Fetching ${type.name} transactions from: $url');

      final response = await _apiProvider.request(
        url: url,
        method: Method.GET,
      );

      if (response.statusCode == 200) {
        // Check if response has status field and it's true (for kitty transactions)
        if (type == TransactionType.kitty && response.data["status"] != true) {
          _logger.e('Kitty transactions API returned status: ${response.data["status"]}');
          return [];
        }
        return _parseTransactions(type, response.data);
      } else {
        _logger.e('Failed to fetch transactions: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      _logger.e('Error fetching transactions: $e');
      return [];
    }
  }

  /// Build URL based on transaction type
  String _buildUrl(
    TransactionType type,
    int? entityId,
    String? userId,
    String? phoneNumber,
    int page,
    int size,
    String? accountNo,
  ) {
    switch (type) {
      case TransactionType.user:
        return "${ApiUrls.getUserAllTransactions}?phone_number=$phoneNumber&page=$page&size=$size";

      case TransactionType.kitty:
        // For kitty transactions, entityId is the kittyId
        return "${ApiUrls.contribsTransactions}?kitty_id=${entityId ?? 0}&page=$page&size=$size";

      case TransactionType.chama:
        // Fix: Use proper parameter name and avoid double ampersands
        String baseUrl = "${ApiUrls.getChamaTransactions}?chama_id=${entityId ?? 0}&page=$page&size=$size";
        if (accountNo != null && accountNo.isNotEmpty) {
          baseUrl += "&account_number=$accountNo";  // Fixed: use account_number not account_no
        }
        return baseUrl;

      case TransactionType.event:
        return "${ApiUrls.EVENTTRANSACTIONS}?event_id=${entityId ?? 0}&size=$size&page=$page";
    }
  }

  /// Parse transactions based on type
  List<TransactionModel> _parseTransactions(TransactionType type, dynamic data) {
    List<TransactionModel> transactions = [];

    try {
      switch (type) {
        case TransactionType.user:
          // User transactions: data["items"]
          for (var item in data["items"] ?? []) {
            transactions.add(TransactionModel.fromJson(item));
          }
          break;

        case TransactionType.kitty:
          // Kitty transactions: data["data"]["results"]["items"]
          var items = data["data"]["results"]["items"] ?? [];
          _logger.i('Parsing ${items.length} kitty transactions');
          for (var item in items) {
            transactions.add(TransactionModel.fromJson(item));
          }
          break;

        case TransactionType.chama:
          // Chama transactions: data["data"]["items"] (not transactions!)
          for (var item in data["data"]["items"] ?? []) {
            // Convert chama Transaction to TransactionModel
            chama.Transaction chamaTransaction = chama.Transaction.fromJson(item);
            transactions.add(_convertChamaTransactionToTransactionModel(chamaTransaction));
          }
          break;

        case TransactionType.event:
          // Event transactions: data["data"]["items"]
          for (var item in data["data"]["items"] ?? []) {
            transactions.add(TransactionModel.fromJson(item));
          }
          break;
      }
    } catch (e) {
      _logger.e('Error parsing transactions: $e');
    }

    return transactions;
  }

  /// Convert chama Transaction to TransactionModel
  TransactionModel _convertChamaTransactionToTransactionModel(chama.Transaction chamaTransaction) {
    return TransactionModel(
      id: chamaTransaction.id,
      createdAt: chamaTransaction.createdAt,
      updatedAt: chamaTransaction.updatedAt,
      amount: chamaTransaction.amount,
      firstName: chamaTransaction.firstName,
      secondName: chamaTransaction.secondName,
      phoneNumber: chamaTransaction.phoneNumber,
      transactionCode: chamaTransaction.transactionCode,
      transactionCodeOther: chamaTransaction.transactionCodeOther,
      internalId: chamaTransaction.internalId,
      kittyId: chamaTransaction.kittyId,
      status: chamaTransaction.status,
      transactionType: chamaTransaction.transactionType,
      transactionCategory: chamaTransaction.transactionCategory,
      currencyCode: chamaTransaction.currencyCode,
      typeInOut: chamaTransaction.typeInOut,
      channelCode: chamaTransaction.channelCode,
      metadataId: chamaTransaction.metadataId,
      accountNumber: chamaTransaction.accountNumber,
      payment_ref: chamaTransaction.paymentRef,
      product: 'chama',
    );
  }

  /// Search transactions
  Future<List<TransactionModel>> searchTransactions({
    required TransactionType type,
    required String query,
    required List<TransactionModel> transactions,
  }) async {
    if (query.isEmpty) return transactions;
    
    return transactions.where((transaction) {
      final searchLower = query.toLowerCase();
      return (transaction.firstName?.toLowerCase().contains(searchLower) ?? false) ||
             (transaction.secondName?.toLowerCase().contains(searchLower) ?? false) ||
             (transaction.phoneNumber?.toLowerCase().contains(searchLower) ?? false) ||
             (transaction.transactionRef?.toLowerCase().contains(searchLower) ?? false) ||
             (transaction.transactionCode?.toLowerCase().contains(searchLower) ?? false);
    }).toList();
  }

  /// Filter transactions by date range
  List<TransactionModel> filterByDateRange({
    required List<TransactionModel> transactions,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    if (startDate == null && endDate == null) return transactions;
    
    return transactions.where((transaction) {
      if (transaction.createdAt == null) return false;
      
      final transactionDate = transaction.createdAt!;
      
      if (startDate != null && transactionDate.isBefore(startDate)) {
        return false;
      }
      
      if (endDate != null && transactionDate.isAfter(endDate)) {
        return false;
      }
      
      return true;
    }).toList();
  }
}
