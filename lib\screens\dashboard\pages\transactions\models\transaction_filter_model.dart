// Transaction Filter Model
// Encapsulates all filter parameters for transaction filtering

import 'package:intl/intl.dart';

/// Enum for different filter types
enum FilterType {
  none('Filter'),
  phone('Account No'),
  code('code'),
  date('Date');

  const FilterType(this.displayName);
  final String displayName;

  static FilterType fromString(String value) {
    return FilterType.values.firstWhere(
      (type) => type.displayName == value,
      orElse: () => FilterType.none,
    );
  }
}

/// Model to encapsulate all transaction filter parameters
class TransactionFilterModel {
  final String searchQuery;
  final FilterType selectedFilterType;
  final String phoneNumber;
  final String transactionCode;
  final DateTime? startDate;
  final DateTime? endDate;

  const TransactionFilterModel({
    this.searchQuery = '',
    this.selectedFilterType = FilterType.none,
    this.phoneNumber = '',
    this.transactionCode = '',
    this.startDate,
    this.endDate,
  });

  /// Create a copy with updated values
  TransactionFilterModel copyWith({
    String? searchQuery,
    FilterType? selectedFilterType,
    String? phoneNumber,
    String? transactionCode,
    DateTime? startDate,
    DateTime? endDate,
    bool clearStartDate = false,
    bool clearEndDate = false,
  }) {
    return TransactionFilterModel(
      searchQuery: searchQuery ?? this.searchQuery,
      selectedFilterType: selectedFilterType ?? this.selectedFilterType,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      transactionCode: transactionCode ?? this.transactionCode,
      startDate: clearStartDate ? null : (startDate ?? this.startDate),
      endDate: clearEndDate ? null : (endDate ?? this.endDate),
    );
  }

  /// Check if any filters are active
  bool get hasActiveFilters {
    return searchQuery.isNotEmpty ||
           phoneNumber.isNotEmpty ||
           transactionCode.isNotEmpty ||
           startDate != null ||
           endDate != null;
  }

  /// Check if advanced filters (non-search) are active
  bool get hasAdvancedFilters {
    return phoneNumber.isNotEmpty ||
           transactionCode.isNotEmpty ||
           (startDate != null && endDate != null);
  }

  /// Check if date range filter is complete
  bool get hasCompleteDateRange {
    return startDate != null && endDate != null;
  }

  /// Get formatted start date string
  String get formattedStartDate {
    return startDate != null 
        ? DateFormat('yyyy-MM-dd').format(startDate!)
        : '';
  }

  /// Get formatted end date string
  String get formattedEndDate {
    return endDate != null 
        ? DateFormat('yyyy-MM-dd').format(endDate!)
        : '';
  }

  /// Clear all filters
  TransactionFilterModel clearAll() {
    return const TransactionFilterModel();
  }

  /// Clear specific filter type
  TransactionFilterModel clearFilterType(FilterType type) {
    switch (type) {
      case FilterType.phone:
        return copyWith(
          phoneNumber: '',
          selectedFilterType: FilterType.none,
        );
      case FilterType.code:
        return copyWith(
          transactionCode: '',
          selectedFilterType: FilterType.none,
        );
      case FilterType.date:
        return copyWith(
          clearStartDate: true,
          clearEndDate: true,
          selectedFilterType: FilterType.none,
        );
      case FilterType.none:
        return clearAll();
    }
  }

  /// Convert to API parameters map
  Map<String, dynamic> toApiParams() {
    final params = <String, dynamic>{};
    
    if (searchQuery.isNotEmpty) {
      params['search'] = searchQuery;
    }
    
    if (phoneNumber.isNotEmpty) {
      params['phone_number'] = phoneNumber;
    }
    
    if (transactionCode.isNotEmpty) {
      params['transaction_code'] = transactionCode;
    }
    
    if (hasCompleteDateRange) {
      params['start-date'] = formattedStartDate;
      params['end-date'] = formattedEndDate;
    }
    
    return params;
  }

  /// Validate filter parameters
  FilterValidationResult validate() {
    final errors = <String>[];
    
    // Validate phone number format
    if (phoneNumber.isNotEmpty) {
      if (phoneNumber.length < 9 || phoneNumber.length > 12) {
        errors.add('Phone number must be between 9-12 digits');
      }
      if (!RegExp(r'^\d+$').hasMatch(phoneNumber)) {
        errors.add('Phone number must contain only digits');
      }
    }
    
    // Validate date range
    if (startDate != null && endDate != null) {
      if (startDate!.isAfter(endDate!)) {
        errors.add('Start date must be before end date');
      }
      if (endDate!.isAfter(DateTime.now())) {
        errors.add('End date cannot be in the future');
      }
    }
    
    // Validate transaction code
    if (transactionCode.isNotEmpty && transactionCode.length < 3) {
      errors.add('Transaction code must be at least 3 characters');
    }
    
    return FilterValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TransactionFilterModel &&
           other.searchQuery == searchQuery &&
           other.selectedFilterType == selectedFilterType &&
           other.phoneNumber == phoneNumber &&
           other.transactionCode == transactionCode &&
           other.startDate == startDate &&
           other.endDate == endDate;
  }

  @override
  int get hashCode {
    return Object.hash(
      searchQuery,
      selectedFilterType,
      phoneNumber,
      transactionCode,
      startDate,
      endDate,
    );
  }

  @override
  String toString() {
    return 'TransactionFilterModel('
           'searchQuery: $searchQuery, '
           'selectedFilterType: $selectedFilterType, '
           'phoneNumber: $phoneNumber, '
           'transactionCode: $transactionCode, '
           'startDate: $startDate, '
           'endDate: $endDate)';
  }
}

/// Result of filter validation
class FilterValidationResult {
  final bool isValid;
  final List<String> errors;

  const FilterValidationResult({
    required this.isValid,
    required this.errors,
  });
}
