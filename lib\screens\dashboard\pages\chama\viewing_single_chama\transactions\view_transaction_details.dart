import 'package:flutter/material.dart';
// import 'package:flutter_animated_dialog_updated/flutter_animated_dialog.dart';
import 'package:flutter_animated_dialog_updated/flutter_animated_dialog.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/models/chama/chama_transactions.dart';
import 'package:onekitty/models/chama/get_text_transaction_request.dart';
import 'package:onekitty/screens/dashboard/pages/chama/viewing_single_chama/transactions/export_widget.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/date_picker.dart';
import 'package:onekitty/utils/datetime/combined_datetime.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:share_plus/share_plus.dart';
import 'package:onekitty/main.dart' show isLight;
import '../../../../../../utils/utils_exports.dart';

class SingleChamaTransaction extends StatefulWidget {
  final Transaction item;

  const SingleChamaTransaction({
    super.key,
    required this.item,
  });

  @override
  State<SingleChamaTransaction> createState() => _SingleChamaTransactionState();
}

class _SingleChamaTransactionState extends State<SingleChamaTransaction> {
  TextEditingController dateCtrl = TextEditingController();
  TextEditingController timeCtrl = TextEditingController();
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  final ChamaController chamaController = Get.put(ChamaController());

  @override
  void dispose() {
    dateCtrl.clear();
    timeCtrl.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        showTransactionDialog(
          context: context,
          details: widget.item,
        );
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 15.h, vertical: 16.h),
        margin: EdgeInsets.only(bottom: 4.h),
        decoration: AppDecoration.outlineGray
            .copyWith(borderRadius: BorderRadiusStyle.circleBorder22),
        child: SizedBox(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadiusStyle.roundedBorder6),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: 8.h),
                      child: Container(
                        margin: EdgeInsets.only(top: 3.h, bottom: 2.h),
                        padding: EdgeInsets.all(7.h),
                        decoration: AppDecoration.fillAGray
                            .copyWith(shape: BoxShape.circle),
                        child: Padding(
                          padding: const EdgeInsets.all(6.0),
                          child: Text(
                            '${widget.item.firstName?.isNotEmpty ?? false ? widget.item.firstName![0] : ' '}${widget.item.secondName?.isNotEmpty ?? false ? widget.item.secondName![0] : ' '}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(left: 14.h),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                                '${widget.item.firstName ?? ""} ${widget.item.secondName ?? ""}',
                                style: CustomTextStyles.titleSmallIndigo500),
                            SizedBox(height: 7.h),
                            Opacity(
                                opacity: 0.4,
                                child: Text(
                                    '${widget.item.phoneNumber ?? widget.item.transactionCode}',
                                    style: theme.textTheme.bodySmall?.copyWith(
                                        color: isLight.value
                                            ? appTheme.black900
                                            : appTheme.whiteA700))),
                            Text(widget.item.transactionCode ?? "")
                          ],
                        ),
                      ),
                    ),
                    // const Spacer(),
                    Column(
                      children: [
                        Align(
                            alignment: Alignment.centerRight,
                            child: Text(
                              '${widget.item.typeInOut == "OUT" ? '-' : '+'} ${widget.item.currencyCode ?? "KES"} ${widget.item.amount.toString()}',
                              style: TextStyle(
                                color: '${widget.item.typeInOut}' == "OUT"
                                    ? Colors.red
                                    : Colors.green,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            )

                            // style: TextStyle(
                            //     color: item.transactionType == "Contribution"
                            //         ? Colors.green
                            //         : Colors.red,
                            //     fontWeight: FontWeight.bold,
                            //     fontSize: 12),
                            ),
                        SizedBox(height: 2.h),
                        Opacity(
                          opacity: 0.4,
                          child: Text(
                              DateFormat.jm()
                                  .format(widget.item.createdAt!.toLocal()),
                              style: theme.textTheme.bodySmall?.copyWith(
                                  color: isLight.value
                                      ? appTheme.black900
                                      : appTheme.whiteA700)),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  showTransactionDialog({
    required BuildContext context,
    required Transaction details,
    bool singleTrans = true,
  }) {
    return showAnimatedDialog(
      barrierDismissible: true,
      animationType: DialogTransitionType.sizeFade,
      curve: Curves.fastOutSlowIn,
      duration: const Duration(milliseconds: 900),
      context: context,
      builder: (BuildContext context) {
        final ChamaDataController dataController =
            Get.put(ChamaDataController());
        return Dialog(
          child: SizedBox(
            // height: SizeConfig.screenHeight * .50,
            width: SizeConfig.screenWidth * .8,
            child: Padding(
              padding: EdgeInsets.symmetric(
                  horizontal: getProportionalScreenWidth(10)),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                      margin: EdgeInsets.only(top: 3.h, bottom: 2.h),
                      padding: EdgeInsets.all(7.h),
                      decoration: AppDecoration.fillAGray
                          .copyWith(shape: BoxShape.circle),
                      child: Center(
                        child: Text(
                          '${details.firstName?.isNotEmpty ?? false ? details.firstName![0] : '  '}${details.secondName?.isNotEmpty ?? false ? details.secondName![0] : '  '}',
                          style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold),
                        ),
                      )),
                  SizedBox(height: 9.h),
                  Text(dataController.chama.value.chama?.title ?? '',
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                      style: CustomTextStyles.titleSmallIndigo500),
                  SizedBox(height: 7.h),
                  Text(
                    '${details.typeInOut == "OUT" ? '-' : '+'} ${details.currencyCode ?? "KES"} ${details.amount.toString()}',
                    style: TextStyle(
                      color: '${details.typeInOut}' == "OUT"
                          ? Colors.red
                          : Colors.green,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                  SizedBox(height: 7.h),
                  Text("${details.firstName ?? ""} ${details.secondName ?? ""}",
                      style: const TextStyle()),
                  SizedBox(height: 5.h),
                  Text(details.phoneNumber.toString(),
                      style: const TextStyle()),
                  SizedBox(height: 7.h),
                  Text("Transaction ID: ${details.transactionCode}",
                      style: const TextStyle()),
                  SizedBox(height: 7.h),
                  Text(
                    '${details.status}',
                    style: TextStyle(
                        color: details.status == "SUCCESS"
                            ? Colors.green
                            : details.status
                                        ?.toUpperCase()
                                        .contains("FAILED") ??
                                    false
                                ? Colors.red
                                : const Color(0xFF95730C)),
                  ),
                  SizedBox(height: 8.h),
                  Padding(
                    padding: EdgeInsets.only(bottom: 20.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).canvasColor,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20.sp),
                              side: BorderSide(
                                  width: 2.sp,
                                  color: Theme.of(context).primaryColor),
                            ),
                          ),
                          onPressed: () async {
                            showModalBottomSheet(
                                isScrollControlled: true,
                                context: context,
                                builder: (context) {
                                  return DraggableScrollableSheet(
                                      expand: false,
                                      maxChildSize: 0.97,
                                      initialChildSize: 0.7,
                                      builder: (context, scrollController) {
                                        return Container(
                                          margin: const EdgeInsets.symmetric(
                                              horizontal: 20, vertical: 12),
                                          child: Column(
                                            mainAxisSize: MainAxisSize.min,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.end,
                                            children: [
                                              Text(
                                                "Pick a Date to receive your chama transactions",
                                                style: context.dividerTextLarge
                                                    ?.copyWith(
                                                        fontSize: 14.h,
                                                        decoration:
                                                            TextDecoration
                                                                .underline),
                                              ),
                                              const SizedBox(
                                                height: 12,
                                              ),
                                              DatePicker(
                                                date: dateCtrl,
                                                time: timeCtrl,
                                                isAllow: false,
                                              ),
                                              const SizedBox(
                                                height: 12,
                                              ),
                                              ElevatedButton(
                                                  onPressed: () {
                                                    postTransactionText();
                                                  },
                                                  child: const Text("SHARE")),
                                            ],
                                          ),
                                        );
                                      });
                                });
                          },
                          child: const Text(
                            "Share",
                            style: TextStyle(color: Colors.black),
                          ),
                        ),
                        const Spacer(),
                        CustomElevatedButton(
                          buttonStyle: ButtonStyle(
                            backgroundColor: WidgetStateProperty.all<Color>(
                                const Color.fromARGB(255, 184, 129, 57)),
                          ),
                          width: 90.w,
                          height: 30.h,
                          text: "Export",
                          buttonTextStyle: TextStyle(
                              fontSize: 12.h, fontWeight: FontWeight.bold),
                          leftIcon: Container(
                              margin: EdgeInsets.only(right: 1.w),
                              child: CustomImageView(
                                  imagePath: AssetUrl.expIcon,
                                  height: 12.h,
                                  width: 12.w)),
                          onPressed: () async {
                            showModalBottomSheet(
                                context: context,
                                builder: (BuildContext context) {
                                  return ExportChamaContentWidget(
                                      details: details,
                                      singleTrans: singleTrans);
                                });
                          },
                        ),
                        SizedBox(
                          height: 10.sp,
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  postTransactionText() async {
    final startDate = dateCtrl.text;
    final startTime = timeCtrl.text;

    final combinedStartTime = combineDateTime(startDate, startTime);
    final formattedStartTime = formatDateTime(combinedStartTime);

    final startDateTime = DateTime.tryParse(formattedStartTime);
    ChamaTextTransactions request = ChamaTextTransactions(
        startDate: startDateTime,
        action: "TEXT",
        chamaId: chamaDataController.chama.value.chama?.id);
    bool res = await chamaController.postTransactionText(requst: request);
    if (res) {
      if (!mounted) return;
      String shareMsg = chamaController.transMessage.string;
      await Share.share(shareMsg, subject: "Transaction details");
    } else {
      Snack.show(res, chamaController.apiMessage.string);
    }
  }
}



  // showTransactionDialog({
  //   required BuildContext context,
  //   required Transaction details,
  //   bool singleTrans = true,
  // }) {
  //   return showAnimatedDialog(
  //     barrierDismissible: true,
  //     animationType: DialogTransitionType.sizeFade,
  //     curve: Curves.fastOutSlowIn,
  //     duration: const Duration(milliseconds: 900),
  //     context: context,
  //     builder: (BuildContext context) {
  //       final DateFormat format = DateFormat.MMMEd().add_jms();
  //       DateTime createdAt = details.createdAt!.toLocal();
  //       final ChamaDataController chamaDataController =
  //           Get.put(ChamaDataController());
  //       return Dialog(
  //         child: SizedBox(
  //           // height: SizeConfig.screenHeight * .50,
  //           width: SizeConfig.screenWidth * .8,
  //           child: Padding(
  //             padding: EdgeInsets.symmetric(
  //                 horizontal: getProportionalScreenWidth(10)),
  //             child: Column(
  //               mainAxisAlignment: MainAxisAlignment.center,
  //               mainAxisSize: MainAxisSize.min,
  //               children: [
  //                 Container(
  //                     margin: EdgeInsets.only(top: 7.h, bottom: 2.h, right: 5),
  //                     padding: EdgeInsets.all(7.h),
  //                     decoration: AppDecoration.fillAGray
  //                         .copyWith(shape: BoxShape.circle),
  //                     child: Center(
  //                       child: Text(
  //                         '${details.transferMode?.isNotEmpty ?? false ? details.transferMode![0] : '  '}${details.reason?.isNotEmpty ?? false ? details.reason![0] : '  '}',
  //                         style: const TextStyle(
  //                             color: Colors.white,
  //                             fontSize: 12,
  //                             fontWeight: FontWeight.bold),
  //                       ),
  //                     )),
  //                 SizedBox(height: 9.h),
  //                 Text(chamaDataController.chama.value.chama?.title ?? '',
  //                     overflow: TextOverflow.ellipsis,
  //                     textAlign: TextAlign.center,
  //                     style: CustomTextStyles.titleSmallIndigo500),
  //                 SizedBox(height: 7.h),
  //                 Text(
  //                   details.reason ?? "",
  //                   textAlign: TextAlign.center,
  //                   style: context.dividerTextLarge,
  //                 ),
  //                 SizedBox(height: 9.h),
  //                 Text(
  //                   '-${FormattedCurrency.getFormattedCurrency(details.amount)}',
  //                   style: TextStyle(
  //                     color: Colors.red,
  //                     fontWeight: FontWeight.bold,
  //                     fontSize: 12,
  //                   ),
  //                 ),
  //                 SizedBox(height: 7.h),
  //                 Column(
  //                   crossAxisAlignment: CrossAxisAlignment.start,
  //                   children: [
  //                     rowWidget("Status: ", "${details.status ?? ""}"),
                   
  //                     rowWidget("Initiator: ", " ${details.initator?.firstName} ${details.initator?.secondName} ${details.initator?.role}"),
                      
                      
  //                     rowWidget("Initiated At: ", "${DateFormat.jm().format(details.initiatedAt ?? DateTime.now())}"),
                      
                      
  //                     rowWidget("Total signatories required: ", '${details.totalSignaturesRequired}'),
                      
                     
  //                     rowWidget("Approved By: ", "${details.approvedBy?.firstName} ${details.approvedBy?.secondName}"),
                      
                      
  //                     rowWidget("Receiver Account: ", "${details.receiverAccount ?? ""} ${details.receiverAccountRef ?? ""}"),
                      
                     
  //                     rowWidget("Transfer Mode: ", "${details.transferMode}"),
                     
  //                   ],
  //                 ),
  //                 SizedBox(height: 8.h),
  //                 Padding(
  //                   padding: EdgeInsets.only(bottom: 20.h),
  //                   child: Row(
  //                     mainAxisAlignment: MainAxisAlignment.spaceEvenly,
  //                     children: [
  //                       ElevatedButton(
  //                         style: ElevatedButton.styleFrom(
  //                           backgroundColor: Theme.of(context).canvasColor,
  //                           shape: RoundedRectangleBorder(
  //                             borderRadius: BorderRadius.circular(20.sp),
  //                             side: BorderSide(
  //                                 width: 2.sp,
  //                                 color: Theme.of(context).primaryColor),
  //                           ),
  //                         ),
  //                         onPressed: () async {},
  //                         child: const Text(
  //                           "Share",
  //                           style: TextStyle(color: Colors.black),
  //                         ),
  //                       ),
  //                       Spacer(),
  //                       CustomElevatedButton(
  //                         buttonStyle: ButtonStyle(
  //                           backgroundColor: MaterialStateProperty.all<Color>(
  //                               Color.fromARGB(255, 184, 129, 57)),
  //                         ),
  //                         width: 90.w,
  //                         height: 30.h,
  //                         text: "Export",
  //                         buttonTextStyle: TextStyle(
  //                             fontSize: 12.h, fontWeight: FontWeight.bold),
  //                         leftIcon: Container(
  //                             margin: EdgeInsets.only(right: 1.w),
  //                             child: CustomImageView(
  //                                 imagePath: AssetUrl.expIcon,
  //                                 height: 12.h,
  //                                 width: 12.w)),
  //                         onPressed: () async {},
  //                       ),
  //                       SizedBox(
  //                         height: 10.sp,
  //                       ),
  //                     ],
  //                   ),
  //                 )
  //               ],
  //             ),
  //           ),
  //         ),
  //       );
  //     },
  //   );
  // }

  // Widget rowWidget(String text, String text2) {
  //   return Padding(
  //     padding: const EdgeInsets.only(bottom: 7),
  //     child: Row(
  //       children: [
  //         Text(
  //           text,
  //           style: TextStyle(fontWeight: FontWeight.w700),
  //         ),
  //         Expanded(child: Text(text2))
  //       ],
  //     ),
  //   );
  // }
