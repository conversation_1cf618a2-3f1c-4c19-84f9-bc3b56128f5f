// Transaction Code Filter Widget
// Widget for filtering by transaction code with clear functionality

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/custom_search_view.dart';
import 'package:onekitty/utils/custom_image_view.dart';
import '../../models/transaction_type.dart';
import '../../models/transaction_filter_model.dart';
import '../../controllers/transaction_controller.dart';

class TransactionCodeFilter extends StatelessWidget {
  final TransactionController controller;
  final TransactionType transactionType;

  const TransactionCodeFilter({
    super.key,
    required this.controller,
    required this.transactionType,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: _buildCodeInput(context),
          ),
          Si<PERSON><PERSON><PERSON>(width: 12.w),
          _buildClearButton(context),
        ],
      ),
    );
  }

  /// Build transaction code input field
  Widget _buildCodeInput(BuildContext context) {
    return CustomSearchView(
      controller: controller.codeController,
      hintText: _getHintText(),
      contentPadding: EdgeInsets.symmetric(
        horizontal: 16.w,
        vertical: 12.h,
      ),
      textInputType: TextInputType.text,
      onChanged: (value) => _handleCodeChange(value),
      borderDecoration: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24.r),
        borderSide: BorderSide(
          color: _getBorderColor(context),
          width: 1.w,
        ),
      ),
      fillColor: Theme.of(context).scaffoldBackgroundColor,
      prefix: Icon(
        Icons.receipt_long_outlined,
        size: 20.h,
        color: AppColors.primary,
      ),
      validator: _validateTransactionCode,
    );
  }

  /// Build clear button
  Widget _buildClearButton(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(24.r),
        onTap: () => _clearFilter(),
        child: Container(
          padding: EdgeInsets.all(8.h),
          child: CustomImageView(
            imagePath: AssetUrl.imgIconoirCancel,
            height: 24.h,
            width: 24.w,
            color: AppColors.neutralGrey,
          ),
        ),
      ),
    );
  }

  /// Handle transaction code input changes
  void _handleCodeChange(String value) {
    // Convert to uppercase for consistency
    final upperValue = value.toUpperCase();
    
    // Update controller if different
    if (controller.codeController.text != upperValue) {
      controller.codeController.text = upperValue;
      controller.codeController.selection = TextSelection.fromPosition(
        TextPosition(offset: upperValue.length),
      );
    }
    
    controller.setTransactionCodeFilter(upperValue);
  }

  /// Validate transaction code format
  String? _validateTransactionCode(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Optional field
    }
    
    if (value.length < 3) {
      return 'Code must be at least 3 characters';
    }
    
    if (value.length > 20) {
      return 'Code too long';
    }
    
    // Check for valid characters (alphanumeric)
    if (!RegExp(r'^[A-Z0-9]+$').hasMatch(value.toUpperCase())) {
      return 'Code must contain only letters and numbers';
    }
    
    return null;
  }

  /// Get hint text based on transaction type
  String _getHintText() {
    switch (transactionType) {
      case TransactionType.kitty:
        return "Filter with transaction code";
      case TransactionType.chama:
        return "Enter chama transaction code";
      case TransactionType.event:
        return "Enter event transaction code";
      case TransactionType.user:
        return "Enter transaction code";
    }
  }

  /// Get border color based on validation state
  Color _getBorderColor(BuildContext context) {
    final code = controller.transactionCode;
    
    if (code.isEmpty) {
      return AppColors.neutralGrey.withOpacity(0.3);
    }
    
    final validation = controller.filterModel.validate();
    if (!validation.isValid && validation.errors.any((error) => error.contains('code'))) {
      return Colors.red.withOpacity(0.7);
    }
    
    return AppColors.primary.withOpacity(0.7);
  }

  /// Clear transaction code filter
  void _clearFilter() {
    controller.clearFilterType(FilterType.code);
  }
}

/// Transaction code suggestions based on common patterns
class TransactionCodeSuggestions {
  static List<String> getCommonPrefixes(TransactionType type) {
    switch (type) {
      case TransactionType.kitty:
        return ['KT', 'KITTY', 'MP', 'MPESA'];
      case TransactionType.chama:
        return ['CH', 'CHAMA', 'GRP', 'GROUP'];
      case TransactionType.event:
        return ['EV', 'EVENT', 'TK', 'TICKET'];
      case TransactionType.user:
        return ['USR', 'USER', 'TXN', 'TRANS'];
    }
  }

  static bool isValidFormat(String code, TransactionType type) {
    final upperCode = code.toUpperCase();
    final prefixes = getCommonPrefixes(type);
    
    // Check if code starts with any common prefix
    return prefixes.any((prefix) => upperCode.startsWith(prefix)) ||
           // Or if it's a generic alphanumeric code
           RegExp(r'^[A-Z0-9]{3,}$').hasMatch(upperCode);
  }
}

/// Auto-complete widget for transaction codes
class TransactionCodeAutoComplete extends StatelessWidget {
  final TransactionController controller;
  final TransactionType transactionType;

  const TransactionCodeAutoComplete({
    super.key,
    required this.controller,
    required this.transactionType,
  });

  @override
  Widget build(BuildContext context) {
    return Autocomplete<String>(
      optionsBuilder: (TextEditingValue textEditingValue) {
        if (textEditingValue.text.isEmpty) {
          return const Iterable<String>.empty();
        }
        
        final suggestions = TransactionCodeSuggestions.getCommonPrefixes(transactionType);
        return suggestions.where((option) {
          return option.toLowerCase().contains(textEditingValue.text.toLowerCase());
        });
      },
      onSelected: (String selection) {
        controller.setTransactionCodeFilter(selection);
      },
      fieldViewBuilder: (context, textEditingController, focusNode, onFieldSubmitted) {
        // Sync with main controller
        textEditingController.text = controller.transactionCode;
        
        return CustomSearchView(
          controller: textEditingController,
          focusNode: focusNode,
          hintText: "Enter transaction code",
          contentPadding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 12.h,
          ),
          onChanged: (value) => controller.setTransactionCodeFilter(value.toUpperCase()),
          prefix: Icon(
            Icons.receipt_long_outlined,
            size: 20.h,
            color: AppColors.primary,
          ),
        );
      },
    );
  }
}
