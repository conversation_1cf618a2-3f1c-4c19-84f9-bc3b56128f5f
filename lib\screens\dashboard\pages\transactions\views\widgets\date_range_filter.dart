// Date Range Filter Widget
// Date range picker widget with start/end date selection

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/custom_image_view.dart';
import '../../models/transaction_type.dart';
import '../../models/transaction_filter_model.dart';
import '../../controllers/transaction_controller.dart';

class DateRangeFilter extends StatelessWidget {
  final TransactionController controller;
  final TransactionType transactionType;

  const DateRangeFilter({
    super.key,
    required this.controller,
    required this.transactionType,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Start Date Field
          Expanded(
            child: _buildDateField(
              context: context,
              controller: controller.startDateController,
              labelText: 'Start Date',
              isStartDate: true,
            ),
          ),
          
          SizedBox(width: 8.w),
          
          // End Date Field
          Expanded(
            child: _buildDateField(
              context: context,
              controller: controller.endDateController,
              labelText: 'End Date',
              isStartDate: false,
            ),
          ),
          
          SizedBox(width: 8.w),
          
          // Clear Filter Button
          _buildClearButton(context),
        ],
      ),
    );
  }

  /// Build individual date field
  Widget _buildDateField({
    required BuildContext context,
    required TextEditingController controller,
    required String labelText,
    required bool isStartDate,
  }) {
    return SizedBox(
      height: 50.h,
      child: TextFormField(
        controller: controller,
        style: TextStyle(fontSize: 14.sp),
        readOnly: true,
        onTap: () => _selectDate(context, isStartDate),
        decoration: InputDecoration(
          labelText: labelText,
          labelStyle: TextStyle(
            fontSize: 12.sp,
            color: AppColors.neutralGrey,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.r),
            borderSide: BorderSide(
              color: AppColors.neutralGrey.withOpacity(0.3),
              width: 1.w,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.r),
            borderSide: BorderSide(
              color: AppColors.primary,
              width: 1.w,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.r),
            borderSide: BorderSide(
              color: _getBorderColor(context, isStartDate),
              width: 1.w,
            ),
          ),
          suffixIcon: Icon(
            Icons.calendar_today,
            size: 18.h,
            color: AppColors.primary,
          ),
          contentPadding: EdgeInsets.symmetric(
            horizontal: 12.w,
            vertical: 8.h,
          ),
          filled: true,
          fillColor: Theme.of(context).scaffoldBackgroundColor,
        ),
      ),
    );
  }

  /// Build clear button
  Widget _buildClearButton(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(24.r),
        onTap: () => _clearFilter(),
        child: Container(
          padding: EdgeInsets.all(8.h),
          child: CustomImageView(
            imagePath: AssetUrl.imgIconoirCancel,
            height: 24.h,
            width: 24.w,
            color: AppColors.neutralGrey,
          ),
        ),
      ),
    );
  }

  /// Select date using date picker
  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime initialDate = isStartDate 
        ? (controller.startDate ?? DateTime.now())
        : (controller.endDate ?? DateTime.now());
    
    final DateTime firstDate = DateTime(2020);
    final DateTime lastDate = DateTime.now().add(const Duration(days: 1));

    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black87,
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      final formattedDate = DateFormat('yyyy-MM-dd').format(pickedDate);
      
      if (isStartDate) {
        controller.startDateController.text = formattedDate;
        // If end date is before start date, clear it
        if (controller.endDate != null && pickedDate.isAfter(controller.endDate!)) {
          controller.endDateController.clear();
          controller.setDateRangeFilter(pickedDate, null);
        } else {
          controller.setDateRangeFilter(pickedDate, controller.endDate);
        }
      } else {
        controller.endDateController.text = formattedDate;
        // If start date is after end date, clear it
        if (controller.startDate != null && pickedDate.isBefore(controller.startDate!)) {
          controller.startDateController.clear();
          controller.setDateRangeFilter(null, pickedDate);
        } else {
          controller.setDateRangeFilter(controller.startDate, pickedDate);
        }
      }
    }
  }

  /// Get border color based on validation state
  Color _getBorderColor(BuildContext context, bool isStartDate) {
    final hasDate = isStartDate 
        ? controller.startDate != null
        : controller.endDate != null;
    
    if (!hasDate) {
      return AppColors.neutralGrey.withOpacity(0.3);
    }
    
    final validation = controller.filterModel.validate();
    if (!validation.isValid && validation.errors.any((error) => error.contains('date'))) {
      return Colors.red.withOpacity(0.7);
    }
    
    return AppColors.primary.withOpacity(0.7);
  }

  /// Clear date range filter
  void _clearFilter() {
    controller.clearFilterType(FilterType.date);
  }
}

/// Date range preset options
class DateRangePresets {
  static List<DateRangePreset> getPresets() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    return [
      DateRangePreset(
        name: 'Today',
        startDate: today,
        endDate: today,
      ),
      DateRangePreset(
        name: 'Yesterday',
        startDate: today.subtract(const Duration(days: 1)),
        endDate: today.subtract(const Duration(days: 1)),
      ),
      DateRangePreset(
        name: 'Last 7 days',
        startDate: today.subtract(const Duration(days: 7)),
        endDate: today,
      ),
      DateRangePreset(
        name: 'Last 30 days',
        startDate: today.subtract(const Duration(days: 30)),
        endDate: today,
      ),
      DateRangePreset(
        name: 'This month',
        startDate: DateTime(now.year, now.month, 1),
        endDate: today,
      ),
      DateRangePreset(
        name: 'Last month',
        startDate: DateTime(now.year, now.month - 1, 1),
        endDate: DateTime(now.year, now.month, 0),
      ),
    ];
  }
}

/// Date range preset model
class DateRangePreset {
  final String name;
  final DateTime startDate;
  final DateTime endDate;

  const DateRangePreset({
    required this.name,
    required this.startDate,
    required this.endDate,
  });
}

/// Date range preset selector widget
class DateRangePresetSelector extends StatelessWidget {
  final TransactionController controller;
  final VoidCallback? onPresetSelected;

  const DateRangePresetSelector({
    super.key,
    required this.controller,
    this.onPresetSelected,
  });

  @override
  Widget build(BuildContext context) {
    final presets = DateRangePresets.getPresets();
    
    return Container(
      height: 40.h,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: presets.length,
        separatorBuilder: (context, index) => SizedBox(width: 8.w),
        itemBuilder: (context, index) {
          final preset = presets[index];
          return _buildPresetChip(context, preset);
        },
      ),
    );
  }

  Widget _buildPresetChip(BuildContext context, DateRangePreset preset) {
    return ActionChip(
      label: Text(
        preset.name,
        style: TextStyle(
          fontSize: 12.sp,
          color: AppColors.primary,
        ),
      ),
      onPressed: () {
        controller.setDateRangeFilter(preset.startDate, preset.endDate);
        onPresetSelected?.call();
      },
      backgroundColor: AppColors.primary.withOpacity(0.1),
      side: BorderSide(
        color: AppColors.primary.withOpacity(0.3),
        width: 1.w,
      ),
    );
  }
}
