// Phone Number Filter Widget
// Dedicated widget for phone number filtering with validation

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/custom_search_view.dart';
import 'package:onekitty/utils/custom_image_view.dart';
import '../../models/transaction_type.dart';
import '../../models/transaction_filter_model.dart';
import '../../controllers/transaction_controller.dart';

class PhoneNumberFilter extends StatelessWidget {
  final TransactionController controller;
  final TransactionType transactionType;

  const PhoneNumberFilter({
    super.key,
    required this.controller,
    required this.transactionType,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: _buildPhoneNumberInput(context),
          ),
          <PERSON><PERSON><PERSON><PERSON>(width: 12.w),
          _buildClearButton(context),
        ],
      ),
    );
  }

  /// Build phone number input field
  Widget _buildPhoneNumberInput(BuildContext context) {
    return CustomSearchView(
      controller: controller.phoneController,
      hintText: _getHintText(),
      contentPadding: EdgeInsets.symmetric(
        horizontal: 16.w,
        vertical: 12.h,
      ),
      textInputType: TextInputType.phone,
      onChanged: (value) => _handlePhoneNumberChange(value),
      borderDecoration: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24.r),
        borderSide: BorderSide(
          color: _getBorderColor(context),
          width: 1.w,
        ),
      ),
      fillColor: Theme.of(context).scaffoldBackgroundColor,
      prefix: Icon(
        Icons.phone_outlined,
        size: 20.h,
        color: AppColors.primary,
      ),
      validator: _validatePhoneNumber,
    );
  }

  /// Build clear button
  Widget _buildClearButton(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(24.r),
        onTap: () => _clearFilter(),
        child: Container(
          padding: EdgeInsets.all(8.h),
          child: CustomImageView(
            imagePath: AssetUrl.imgIconoirCancel,
            height: 24.h,
            width: 24.w,
            color: AppColors.neutralGrey,
          ),
        ),
      ),
    );
  }

  /// Handle phone number input changes
  void _handlePhoneNumberChange(String value) {
    // Remove any non-digit characters
    final cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
    
    // Handle Kenyan phone number format
    if (cleanValue.length == 10 && cleanValue.startsWith('0')) {
      // Convert 0712345678 to 712345678
      final formattedNumber = cleanValue.substring(1);
      controller.phoneController.text = formattedNumber;
      controller.phoneController.selection = TextSelection.fromPosition(
        TextPosition(offset: formattedNumber.length),
      );
      controller.setPhoneNumberFilter(formattedNumber);
    } else if (cleanValue.length <= 12) {
      // Allow input up to 12 digits
      controller.setPhoneNumberFilter(cleanValue);
    }
  }

  /// Validate phone number format
  String? _validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Optional field
    }
    
    final cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
    
    if (cleanValue.length < 9) {
      return 'Phone number too short';
    }
    
    if (cleanValue.length > 12) {
      return 'Phone number too long';
    }
    
    // Check for valid Kenyan phone number patterns
    if (!_isValidKenyanNumber(cleanValue)) {
      return 'Invalid phone number format';
    }
    
    return null;
  }

  /// Check if phone number matches Kenyan patterns
  bool _isValidKenyanNumber(String number) {
    // Kenyan mobile numbers start with 7 (after removing country code)
    // or with 254 (country code) followed by 7
    if (number.length == 9 && number.startsWith('7')) {
      return true;
    }
    
    if (number.length == 12 && number.startsWith('254') && number[3] == '7') {
      return true;
    }
    
    return false;
  }

  /// Get hint text based on transaction type
  String _getHintText() {
    switch (transactionType) {
      case TransactionType.kitty:
      case TransactionType.chama:
        return "Filter by phone number";
      case TransactionType.user:
        return "Enter phone number";
      case TransactionType.event:
        return "Filter by phone number";
    }
  }

  /// Get border color based on validation state
  Color _getBorderColor(BuildContext context) {
    final phoneNumber = controller.phoneNumber;
    
    if (phoneNumber.isEmpty) {
      return AppColors.neutralGrey.withOpacity(0.3);
    }
    
    final validation = controller.filterModel.validate();
    if (!validation.isValid && validation.errors.any((error) => error.contains('Phone'))) {
      return Colors.red.withOpacity(0.7);
    }
    
    return AppColors.primary.withOpacity(0.7);
  }

  /// Clear phone number filter
  void _clearFilter() {
    controller.clearFilterType(FilterType.phone);
  }
}

/// Phone number input formatter for Kenyan numbers
class KenyanPhoneNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Remove any non-digit characters
    final digitsOnly = newValue.text.replaceAll(RegExp(r'[^\d]'), '');
    
    // Limit to 12 digits
    if (digitsOnly.length > 12) {
      return oldValue;
    }
    
    String formatted = digitsOnly;
    
    // Format based on length
    if (digitsOnly.length > 3 && digitsOnly.length <= 6) {
      formatted = '${digitsOnly.substring(0, 3)} ${digitsOnly.substring(3)}';
    } else if (digitsOnly.length > 6 && digitsOnly.length <= 9) {
      formatted = '${digitsOnly.substring(0, 3)} ${digitsOnly.substring(3, 6)} ${digitsOnly.substring(6)}';
    } else if (digitsOnly.length > 9) {
      formatted = '${digitsOnly.substring(0, 3)} ${digitsOnly.substring(3, 6)} ${digitsOnly.substring(6, 9)} ${digitsOnly.substring(9)}';
    }
    
    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
