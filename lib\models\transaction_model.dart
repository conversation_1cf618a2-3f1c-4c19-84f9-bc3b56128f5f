// To parse this JSON data, do
//
//     final transac = transacFromJson(jsonString);

import 'dart:convert';

import 'package:onekitty/models/events/tickets_model.dart';

Transac transacFromJson(String str) => Transac.fromJson(json.decode(str));

String transacToJson(Transac data) => json.encode(data.toJson());

class Transac {
  bool? status;
  String? message;
  transData? data;

  Transac({
    this.status,
    this.message,
    this.data,
  });

  factory Transac.fromJson(Map<String, dynamic> json) => Transac(
        status: json["status"],
        message: json["message"],
        data: json["data"] != null ? transData.fromJson(json["data"]) : null,
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };
}

class transData {
  List<TransactionModel>? items;
  int? page;
  int? size;
  int? maxPage;
  int? totalPages;
  int? total;
  bool? last;
  bool? first;
  int? visible;

  transData({
    this.items,
    this.page,
    this.size,
    this.maxPage,
    this.totalPages,
    this.total,
    this.last,
    this.first,
    this.visible,
  });

  factory transData.fromJson(Map<String, dynamic> json) => transData(
        items: json["items"] != null
            ? List<TransactionModel>.from(
                json["items"].map((x) => TransactionModel.fromJson(x)))
            : null,
        page: json["page"] != null ? int.tryParse(json["page"].toString()) : null,
        size: json["size"] != null ? int.tryParse(json["size"].toString()) : null,
        maxPage: json["max_page"] != null ? int.tryParse(json["max_page"].toString()) : null,
        totalPages: json["total_pages"] != null ? int.tryParse(json["total_pages"].toString()) : null,
        total: json["total"] != null ? int.tryParse(json["total"].toString()) : null,
        last: json["last"],
        first: json["first"],
        visible: json["visible"] != null ? int.tryParse(json["visible"].toString()) : null,
      );

  Map<String, dynamic> toJson() => {
        "items": List<dynamic>.from(items!.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "max_page": maxPage,
        "total_pages": totalPages,
        "total": total,
        "last": last,
        "first": first,
        "visible": visible,
      };
}

class TransactionModel {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final int? merchantId;
  final Merchant? merchant;
  final num? amount;
  final num? merchantBalance;
  final String? transactionRef;
  final int? kittyId;
  final int? transactionStatus;
  final String? email;
  final int? eventId;
  final String? firstName;
  final String? secondName;
  final String? transactionCode;
  final String? transactionCodeOther;
  final String? internalId;
  final String? phoneNumber;
  final String? accounNumber;
  final String? accounNumberRef;
  final String? checkoutRequestId;
  final String? channelCode;
  final String? transactionDate;
  final String? transAmount;
  final String? status;
  final String? transactionType;
  final String? transactionCategory;
  final String? currencyCode;
  final String? typeInOut;
  final String? product;
  final int? metadataId;
  final bool? showNumber;
  final bool? showNames;
  final List<TransactionTicket>? transactionTicket;
  final String? payment_ref;
  final String? accountNumber;
  final String? fullName;
  final dynamic totalCharges;
  final num? orgCharges;
  final num? thirdPartyCharges;
  final String? kittyTitle;
  TransactionModel({
    this.accountNumber,
    this.fullName,
    this.totalCharges,
    this.orgCharges,
    this.thirdPartyCharges,
    this.kittyTitle,
    this.email,
    this.eventId,
    this.firstName,
    this.secondName,
    this.transactionCode,
    this.transactionCodeOther,
    this.internalId,
    this.phoneNumber,
    this.accounNumber,
    this.accounNumberRef,
    this.checkoutRequestId,
    this.channelCode,
    this.transactionDate,
    this.transAmount,
    this.status,
    this.transactionType,
    this.transactionCategory,
    this.currencyCode,
    this.typeInOut,
    this.product,
    this.metadataId,
    this.showNumber,
    this.showNames,
    this.transactionTicket,
    this.payment_ref,
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.merchantId,
    this.merchant,
    this.merchantBalance,
    this.amount,
    this.transactionRef,
    this.kittyId,
    this.transactionStatus,
  });

  factory TransactionModel.fromJson(Map<String, dynamic> json) => TransactionModel(
        id: json["ID"] != null ? int.tryParse(json["ID"].toString()) : null,
        createdAt: json["CreatedAt"] != null ? DateTime.tryParse(json["CreatedAt"].toString()) : null,
        updatedAt: json["UpdatedAt"] != null ? DateTime.tryParse(json["UpdatedAt"].toString()) : null,
        deletedAt: json["DeletedAt"] != null ? DateTime.tryParse(json["DeletedAt"].toString()) : null,
        merchantId: json["merchantID"] != null ? int.tryParse(json["merchantID"].toString()) : null,
        merchant: json["merchant"] != null ? Merchant.fromJson(json["merchant"]) : null,
        amount: json["amount"] != null ? num.tryParse(json["amount"].toString()) : null,
        merchantBalance: json["merchant_balance"] != null ? num.tryParse(json["merchant_balance"].toString()) : null,
        transactionRef: json["transaction_ref"],
        kittyId: json["kitty_id"] != null ? int.tryParse(json["kitty_id"].toString()) : null,
        transactionStatus: json["transaction_status"] != null ? int.tryParse(json["transaction_status"].toString()) : null,
        accounNumber: json["account_number"],
        accounNumberRef: json["account_number_ref"],
        firstName: json["first_name"] ?? "",
        secondName: json["second_name"] ?? "",
        currencyCode: json["currency_code"],
        typeInOut: json["type_in_out"],
        transactionCode: json["transaction_code"],
        transactionCodeOther: json["transaction_code_other"],
        internalId: json["internal_id"],
        phoneNumber: json["phone_number"] ?? "",
        checkoutRequestId: json["checkout_request_id"],
        channelCode: json["channel_code"],
        transactionDate: json["transaction_date"],
        transAmount: json["trans_amount"],
        status: json["status"],
        transactionType: json["transaction_type"],
        transactionCategory: json["transaction_category"],
        product: json["product"],
        metadataId: json["metadata_id"] != null ? int.tryParse(json["metadata_id"].toString()) : null,
        showNumber: json["show_number"],
        showNames: json["show_names"],
        email: json['email'],
        transactionTicket: json["transaction_ticket"] != null
            ? List<TransactionTicket>.from(json["transaction_ticket"]
                .map((x) => TransactionTicket.fromJson(x)))
            : null,
        accountNumber: json["account_number"] ?? '',
        fullName: "${json["first_name"] ?? ''} ${json["second_name"] ?? ''}",
        totalCharges: json["total_charges"],
        orgCharges: json["org_charges"] != null ? num.tryParse(json["org_charges"].toString()) : null,
        thirdPartyCharges: json["third_party_charges"] != null ? num.tryParse(json["third_party_charges"].toString()) : null,
        kittyTitle: json["kitty_title"] ?? '',
        payment_ref: json["payment_ref"] ?? '',
      );
/// Check if this transaction is editable (only kitty transactions)
  bool get isEditable => product?.toLowerCase() == 'contributions';

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toUtc().toIso8601String(),
        "UpdatedAt": updatedAt?.toUtc().toIso8601String(),
        "DeletedAt": deletedAt?.toUtc().toIso8601String(),
        "merchantID": merchantId,
        "merchant": merchant?.toJson(),
        "amount": amount,
        "merchant_balance": merchantBalance,
        "transaction_ref": transactionRef,
        "kitty_id": kittyId,
        "transaction_status": transactionStatus,
         "first_name": firstName ?? "",
        "currency_code": currencyCode,
        "second_name": secondName ?? "",
        "transaction_code": transactionCode,
        "transaction_code_other": transactionCodeOther,
        "internal_id": internalId,
        "phone_number": phoneNumber ?? "",
        "type_in_out": typeInOut,
        "checkout_request_id": checkoutRequestId,
        "channel_code": channelCode,
        "transaction_date": transactionDate,
        "trans_amount": transAmount,
        "status": status,
        "transaction_type": transactionType,
        "transaction_category": transactionCategory,
        "product": product,
        "metadata_id": metadataId,
        "show_number": showNumber,
        "show_names": showNames,
        "email": email ?? "",
        "transaction_ticket": transactionTicket != null
            ? List<dynamic>.from(transactionTicket!.map((x) => x.toJson()))
            : null,
        "account_number": accountNumber ?? '',
        "full_name": "${firstName ?? ''} ${secondName ?? ''}",
        "total_charges": totalCharges,
        "org_charges": orgCharges,
        "third_party_charges": thirdPartyCharges,
        "kitty_title": kittyTitle ?? '',
        "payment_ref": payment_ref ?? '',
      };
}

class Merchant {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  int? userId;
  String? merchantName;
  num? merchantCode;
  num? merchantPercent;

  Merchant({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.userId,
    this.merchantName,
    this.merchantCode,
    this.merchantPercent,
  });

  factory Merchant.fromJson(Map<String, dynamic> json) => Merchant(
        id: json["ID"] != null ? int.tryParse(json["ID"].toString()) : null,
        createdAt: json["CreatedAt"] != null ? DateTime.tryParse(json["CreatedAt"].toString()) : null,
        updatedAt: json["UpdatedAt"] != null ? DateTime.tryParse(json["UpdatedAt"].toString()) : null,
        deletedAt: json["DeletedAt"],
        userId: json["UserID"] != null ? int.tryParse(json["UserID"].toString()) : null,
        merchantName: json["merchant_name"],
        merchantCode: json["merchant_code"] != null ? num.tryParse(json["merchant_code"].toString()) : null,
        merchantPercent: json["merchant_percent"] != null ? num.tryParse(json["merchant_percent"].toString()) : null,
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "UserID": userId,
        "merchant_name": merchantName,
        "merchant_code": merchantCode,
        "merchant_percent": merchantPercent,
      };
}

class TransactionTicket {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final num? amount;
  final num? quantity;
  final dynamic confirmedById;
  final ConfirmedBy? confirmedBy;
  final String? status;
  final int? ticketId;
  final Ticket? ticket;
  final int? transactionId;
  final Transaction? transaction;
  final String? internalReference;

  TransactionTicket({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.amount,
    this.quantity,
    this.confirmedById,
    this.confirmedBy,
    this.status,
    this.ticketId,
    this.ticket,
    this.transactionId,
    this.transaction,
    this.internalReference = '',
  });

  factory TransactionTicket.fromJson(Map<String, dynamic> json) =>
      TransactionTicket(
        id: json["ID"] != null ? int.tryParse(json["ID"].toString()) : null,
        createdAt: json["CreatedAt"] != null
            ? DateTime.tryParse(json["CreatedAt"].toString())
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.tryParse(json["UpdatedAt"].toString())
            : null,
        deletedAt: json["DeletedAt"],
        amount: json["amount"] != null ? num.tryParse(json["amount"].toString()) : null,
        quantity: json["quantity"] != null ? num.tryParse(json["quantity"].toString()) : null,
        confirmedById: json["confirmed_by_id"],
        confirmedBy: json["confirmed_by"] != null
            ? ConfirmedBy.fromJson(json["confirmed_by"])
            : null,
        status: json["status"] ?? '',
        ticketId: json["ticket_id"] != null ? int.tryParse(json["ticket_id"].toString()) : null,
        ticket: json["ticket"] != null ? Ticket.fromJson(json["ticket"]) : null,
        transactionId: json["transaction_id"] != null ? int.tryParse(json["transaction_id"].toString()) : null,
        transaction: json["transaction"] != null
            ? Transaction.fromJson(json["transaction"])
            : null,
        internalReference: json["internal_reference"] ?? '',
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String() ?? '',
        "UpdatedAt": updatedAt?.toIso8601String() ?? '',
        "DeletedAt": deletedAt,
        "amount": amount,
        "quantity": quantity,
        "confirmed_by_id": confirmedById,
        "confirmed_by": confirmedBy?.toJson(),
        "status": status,
        "ticket_id": ticketId,
        "ticket": ticket?.toJson(),
        "transaction_id": transactionId,
        "transaction": transaction?.toJson(),
        "internal_reference": internalReference ?? '',
      };
}

class Transaction {
  bool? status;
  String? message;
  DataClass? data;

  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;

  Transaction({
    this.status,
    this.message,
    this.data,
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
  });

  factory Transaction.fromJson(Map<String, dynamic> json) => Transaction(
        status: json["status"],
        message: json["message"],
        data: json["data"] != null ? DataClass.fromJson(json["data"]) : null,
        id: json["ID"] != null ? int.tryParse(json["ID"].toString()) : null,
        createdAt: json["CreatedAt"] != null
            ? DateTime.tryParse(json["CreatedAt"].toString())
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.tryParse(json["UpdatedAt"].toString())
            : null,
        deletedAt: json["DeletedAt"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String() ?? '',
        "UpdatedAt": updatedAt?.toIso8601String() ?? '',
        "DeletedAt": deletedAt,
      };
} 

Transaction transactionModelFromJson(String str) =>
    Transaction.fromJson(json.decode(str));

String transactionModelToJson(Transaction data) => json.encode(data.toJson());

class DataClass {
  Results? results;

  DataClass({
    this.results,
  });

  factory DataClass.fromJson(Map<String, dynamic> json) => DataClass(
        results:
            json["results"] != null ? Results.fromJson(json["results"]) : null,
      );

  Map<String, dynamic> toJson() => {
        "results": results?.toJson(),
      };
}

class Results {
  List<TransactionModel>? items;
  int? page;
  int? size;
  int? maxPage;
  int? totalPages;
  int? total;
  bool? last;
  bool? first;
  int? visible;

  Results({
    this.items,
    this.page,
    this.size,
    this.maxPage,
    this.totalPages,
    this.total,
    this.last,
    this.first,
    this.visible,
  });

  factory Results.fromJson(Map<String, dynamic> json) => Results(
        items: json["items"] != null
            ? List<TransactionModel>.from(
                json["items"].map((x) => TransactionModel.fromJson(x)))
            : null,
        page: json["page"] != null ? int.tryParse(json["page"].toString()) : null,
        size: json["size"] != null ? int.tryParse(json["size"].toString()) : null,
        maxPage: json["max_page"] != null ? int.tryParse(json["max_page"].toString()) : null,
        totalPages: json["total_pages"] != null ? int.tryParse(json["total_pages"].toString()) : null,
        total: json["total"] != null ? int.tryParse(json["total"].toString()) : null,
        last: json["last"],
        first: json["first"],
        visible: json["visible"] != null ? int.tryParse(json["visible"].toString()) : null,
      );

  Map<String, dynamic> toJson() => {
        "items": List<dynamic>.from(items!.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "max_page": maxPage,
        "total_pages": totalPages,
        "total": total,
        "last": last,
        "first": first,
        "visible": visible,
      };
}

 
class ConfirmedBy {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? phoneNumber;
  final String? firstName;
  final String? secondName;
  final String? email;
  final int? idNumber;
  final num? balance;
  final String? birthDate;
  final String? countryCode;
  final String? county;
  final String? subCounty;
  final String? latitude;
  final String? longitude;
  final String? secondaryNumber;
  final String? profileUrl;
  final int? status;

  ConfirmedBy({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.phoneNumber = '',
    this.firstName = '',
    this.secondName = '',
    this.email = '',
    this.idNumber,
    this.balance,
    this.birthDate = '',
    this.countryCode = '',
    this.county = '',
    this.subCounty = '',
    this.latitude = '',
    this.longitude = '',
    this.secondaryNumber = '',
    this.profileUrl = '',
    this.status,
  });

  factory ConfirmedBy.fromJson(Map<String, dynamic> json) => ConfirmedBy(
        id: json["ID"] != null ? int.tryParse(json["ID"].toString()) : null,
        createdAt: json["CreatedAt"] != null
            ? DateTime.tryParse(json["CreatedAt"].toString())
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.tryParse(json["UpdatedAt"].toString())
            : null,
        deletedAt: json["DeletedAt"],
        phoneNumber: json["phone_number"] ?? '',
        firstName: json["first_name"] ?? '',
        secondName: json["second_name"] ?? '',
        email: json["email"] ?? '',
        idNumber: json["id_number"] != null ? int.tryParse(json["id_number"].toString()) : null,
        balance: json["balance"] != null ? num.tryParse(json["balance"].toString()) : null,
        birthDate: json["birth_date"] ?? '',
        countryCode: json["country_code"] ?? '',
        county: json["county"] ?? '',
        subCounty: json["sub_county"] ?? '',
        latitude: json["latitude"] ?? '',
        longitude: json["longitude"] ?? '',
        secondaryNumber: json["secondary_number"] ?? '',
        profileUrl: json["profile_url"] ?? '',
        status: json["status"] != null ? int.tryParse(json["status"].toString()) : null,
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String() ?? '',
        "UpdatedAt": updatedAt?.toIso8601String() ?? '',
        "DeletedAt": deletedAt,
        "phone_number": phoneNumber ?? '',
        "first_name": firstName ?? '',
        "second_name": secondName ?? '',
        "email": email ?? '',
        "id_number": idNumber,
        "balance": balance,
        "birth_date": birthDate ?? '',
        "country_code": countryCode ?? '',
        "county": county ?? '',
        "sub_county": subCounty ?? '',
        "latitude": latitude ?? '',
        "longitude": longitude ?? '',
        "secondary_number": secondaryNumber ?? '',
        "profile_url": profileUrl ?? '',
        "status": status,
      };
} 
