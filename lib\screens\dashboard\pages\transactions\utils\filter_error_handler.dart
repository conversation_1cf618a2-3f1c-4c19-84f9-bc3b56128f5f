// Filter Error Handler
// Comprehensive error handling and loading states for filtering operations

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/helpers/colors.dart';
import '../models/transaction_filter_model.dart';

/// Error types for filtering operations
enum FilterErrorType {
  network,
  validation,
  timeout,
  serverError,
  noData,
  unauthorized,
  unknown,
}

/// Filter error model
class FilterError {
  final FilterErrorType type;
  final String message;
  final String? details;
  final int? statusCode;

  const FilterError({
    required this.type,
    required this.message,
    this.details,
    this.statusCode,
  });

  factory FilterError.network([String? details]) {
    return FilterError(
      type: FilterErrorType.network,
      message: 'Network connection error',
      details: details ?? 'Please check your internet connection and try again',
    );
  }

  factory FilterError.validation(String message, [String? details]) {
    return FilterError(
      type: FilterErrorType.validation,
      message: message,
      details: details,
    );
  }

  factory FilterError.timeout([String? details]) {
    return FilterError(
      type: FilterErrorType.timeout,
      message: 'Request timeout',
      details: details ?? 'The request took too long to complete',
    );
  }

  factory FilterError.serverError(int statusCode, [String? details]) {
    return FilterError(
      type: FilterErrorType.serverError,
      message: 'Server error ($statusCode)',
      details: details ?? 'An error occurred on the server',
      statusCode: statusCode,
    );
  }

  factory FilterError.noData([String? details]) {
    return FilterError(
      type: FilterErrorType.noData,
      message: 'No data found',
      details: details ?? 'No transactions match your filter criteria',
    );
  }

  factory FilterError.unauthorized([String? details]) {
    return FilterError(
      type: FilterErrorType.unauthorized,
      message: 'Unauthorized access',
      details: details ?? 'You are not authorized to access this data',
    );
  }

  factory FilterError.unknown([String? details]) {
    return FilterError(
      type: FilterErrorType.unknown,
      message: 'Unknown error',
      details: details ?? 'An unexpected error occurred',
    );
  }

  /// Get user-friendly error message
  String get userMessage {
    switch (type) {
      case FilterErrorType.network:
        return 'Connection problem. Please check your internet and try again.';
      case FilterErrorType.validation:
        return message;
      case FilterErrorType.timeout:
        return 'Request timed out. Please try again.';
      case FilterErrorType.serverError:
        return 'Server error. Please try again later.';
      case FilterErrorType.noData:
        return 'No transactions found matching your criteria.';
      case FilterErrorType.unauthorized:
        return 'Access denied. Please log in again.';
      case FilterErrorType.unknown:
        return 'Something went wrong. Please try again.';
    }
  }

  /// Get error icon
  IconData get icon {
    switch (type) {
      case FilterErrorType.network:
        return Icons.wifi_off_rounded;
      case FilterErrorType.validation:
        return Icons.error_outline_rounded;
      case FilterErrorType.timeout:
        return Icons.access_time_rounded;
      case FilterErrorType.serverError:
        return Icons.warning_amber_outlined;
      case FilterErrorType.noData:
        return Icons.search_off_rounded;
      case FilterErrorType.unauthorized:
        return Icons.lock_outline_rounded;
      case FilterErrorType.unknown:
        return Icons.help_outline_rounded;
    }
  }

  /// Get error color
  Color get color {
    switch (type) {
      case FilterErrorType.network:
        return Colors.orange;
      case FilterErrorType.validation:
        return Colors.red;
      case FilterErrorType.timeout:
        return Colors.amber;
      case FilterErrorType.serverError:
        return Colors.red;
      case FilterErrorType.noData:
        return AppColors.neutralGrey;
      case FilterErrorType.unauthorized:
        return Colors.red;
      case FilterErrorType.unknown:
        return Colors.grey;
    }
  }
}

/// Filter error handler utility class
class FilterErrorHandler {
  /// Handle and convert exceptions to FilterError
  static FilterError handleException(dynamic exception) {
    if (exception is FilterError) {
      return exception;
    }

    final errorString = exception.toString().toLowerCase();

    if (errorString.contains('network') || 
        errorString.contains('connection') ||
        errorString.contains('socket')) {
      return FilterError.network(exception.toString());
    }

    if (errorString.contains('timeout')) {
      return FilterError.timeout(exception.toString());
    }

    if (errorString.contains('unauthorized') || 
        errorString.contains('401')) {
      return FilterError.unauthorized(exception.toString());
    }

    if (errorString.contains('server') || 
        errorString.contains('500') ||
        errorString.contains('502') ||
        errorString.contains('503')) {
      return FilterError.serverError(500, exception.toString());
    }

    return FilterError.unknown(exception.toString());
  }

  /// Validate filter model and return validation errors
  static List<FilterError> validateFilterModel(TransactionFilterModel filterModel) {
    final errors = <FilterError>[];
    final validation = filterModel.validate();

    if (!validation.isValid) {
      for (final error in validation.errors) {
        errors.add(FilterError.validation(error));
      }
    }

    return errors;
  }

  /// Show error snackbar
  static void showErrorSnackbar(FilterError error) {
    Get.snackbar(
      'Filter Error',
      error.userMessage,
      icon: Icon(error.icon, color: Colors.white),
      snackPosition: SnackPosition.top,
      backgroundColor: error.color,
      colorText: Colors.white,
      duration: const Duration(seconds: 4),
      margin: EdgeInsets.all(16.w),
      borderRadius: 8.r,
    );
  }

  /// Show validation errors as snackbar
  static void showValidationErrors(List<FilterError> errors) {
    if (errors.isEmpty) return;

    final message = errors.map((e) => e.message).join('\n');
    Get.snackbar(
      'Validation Error',
      message,
      icon: const Icon(Icons.error_outline, color: Colors.white),
      snackPosition: SnackPosition.top,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      duration: const Duration(seconds: 5),
      margin: EdgeInsets.all(16.w),
      borderRadius: 8.r,
    );
  }
}

/// Filter loading state widget
class FilterLoadingIndicator extends StatelessWidget {
  final String? message;
  final bool isVisible;

  const FilterLoadingIndicator({
    super.key,
    this.message,
    this.isVisible = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 24.w,
            height: 24.h,
            child: CircularProgressIndicator(
              strokeWidth: 2.w,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
          ),
          if (message != null) ...[
            SizedBox(height: 8.h),
            Text(
              message!,
              style: TextStyle(
                fontSize: 12.sp,
                color: AppColors.neutralGrey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// Filter error display widget
class FilterErrorDisplay extends StatelessWidget {
  final FilterError error;
  final VoidCallback? onRetry;

  const FilterErrorDisplay({
    super.key,
    required this.error,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            error.icon,
            size: 48.w,
            color: error.color,
          ),
          SizedBox(height: 12.h),
          Text(
            error.userMessage,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.black87,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          if (error.details != null) ...[
            SizedBox(height: 8.h),
            Text(
              error.details!,
              style: TextStyle(
                fontSize: 12.sp,
                color: AppColors.neutralGrey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          if (onRetry != null) ...[
            SizedBox(height: 16.h),
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh_rounded),
              label: const Text('Try Again'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
