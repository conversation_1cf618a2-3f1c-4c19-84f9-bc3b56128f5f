// Unified Transaction Controller
// Manages state and business logic for all transaction types

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/models/auth/user_model.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import '../models/transaction_type.dart';
import '../services/transaction_service.dart';

class TransactionController extends GetxController {
  final TransactionService _transactionService = Get.find<TransactionService>();
  final Logger _logger = Get.find<Logger>();

  // Observable properties
  final RxList<TransactionModel> _transactions = <TransactionModel>[].obs;
  final RxList<TransactionModel> _filteredTransactions = <TransactionModel>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isLoadingMore = false.obs;
  final RxString _searchQuery = ''.obs;
  final RxString _selectedFilter = ''.obs;
  final Rx<DateTime?> _startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> _endDate = Rx<DateTime?>(null);
  final RxInt _currentPage = 0.obs;
  final RxBool _hasMoreData = true.obs;

  // Configuration
  late TransactionPageConfig _config;
  final int _pageSize = 20;

  // Getters
  List<TransactionModel> get transactions => _transactions.toList();
  List<TransactionModel> get filteredTransactions => _filteredTransactions.toList();
  bool get isLoading => _isLoading.value || _isKittyLoading();
  bool get isLoadingMore => _isLoadingMore.value;
  String get searchQuery => _searchQuery.value;
  String get selectedFilter => _selectedFilter.value;
  DateTime? get startDate => _startDate.value;
  DateTime? get endDate => _endDate.value;
  bool get hasMoreData => _hasMoreData.value;
  TransactionPageConfig get config => _config;

  // Check if kitty transactions are loading
  bool _isKittyLoading() {
    if (_config.transactionType == TransactionType.kitty) {
      try {
        final kittyController = Get.find<KittyController>();
        return kittyController.loadingTransactions.value;
      } catch (e) {
        return false;
      }
    }
    return false;
  }

  /// Initialize controller with configuration
  void initialize(TransactionPageConfig config) {
    _config = config;
    _resetState();
    loadTransactions();
  }

  /// Load transactions based on configuration
  Future<void> loadTransactions({bool refresh = false}) async {
    if (refresh) {
      _currentPage.value = 0;
      _hasMoreData.value = true;
      _transactions.clear();
    }

    if (_isLoading.value || (!_hasMoreData.value && !refresh)) return;

    _isLoading.value = true;

    try {
      List<TransactionModel> newTransactions = await _fetchTransactionsForType();
      
      if (refresh) {
        _transactions.assignAll(newTransactions);
      } else {
        _transactions.addAll(newTransactions);
      }

      _hasMoreData.value = newTransactions.length >= _pageSize;
      _currentPage.value++;
      
      _applyFilters();
    } catch (e) {
      _logger.e('Error loading transactions: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load more transactions (pagination)
  Future<void> loadMoreTransactions() async {
    if (_isLoadingMore.value || !_hasMoreData.value) return;

    _isLoadingMore.value = true;

    try {
      List<TransactionModel> newTransactions = await _fetchTransactionsForType();
      _transactions.addAll(newTransactions);
      
      _hasMoreData.value = newTransactions.length >= _pageSize;
      _currentPage.value++;
      
      _applyFilters();
    } catch (e) {
      _logger.e('Error loading more transactions: $e');
    } finally {
      _isLoadingMore.value = false;
    }
  }

  /// Fetch transactions based on type
  Future<List<TransactionModel>> _fetchTransactionsForType() async {
    switch (_config.transactionType) {
      case TransactionType.user:
        final userController = Get.find<UserKittyController>();
        final user = userController.getLocalUser();
        return await _transactionService.fetchTransactions(
          type: TransactionType.user,
          phoneNumber: user?.phoneNumber,
          page: _currentPage.value,
          size: _pageSize,
        );

      case TransactionType.kitty:
        // Use KittyController directly for kitty transactions
        final kittyController = Get.put(KittyController());

        _logger.i('Fetching kitty transactions for kittyId: ${_config.entityId}');

        // If entityId is null or 0, use already loaded transactions (for seeAlltranscScreen route)
        if (_config.entityId == null || _config.entityId == 0) {
          _logger.i('Using already loaded kitty transactions: ${kittyController.transactionsKitty.length}');
          return kittyController.transactionsKitty.toList();
        }

        // Otherwise, load transactions for the specific kitty
        await kittyController.getKittyContributions(
          kittyId: _config.entityId!,
          page: 0,
          size: 100, // Load more transactions at once
        );

        final transactions = kittyController.transactionsKitty.toList();
        _logger.i('Loaded ${transactions.length} kitty transactions');

        // Return all transactions (no pagination for kitty transactions)
        return transactions;

      case TransactionType.chama:
        return await _transactionService.fetchTransactions(
          type: TransactionType.chama,
          entityId: _config.entityId,
          accountNo: _config.accountNo,
          page: _currentPage.value,
          size: _pageSize,
        );

      case TransactionType.event:
        return await _transactionService.fetchTransactions(
          type: TransactionType.event,
          entityId: _config.entityId,
          page: _currentPage.value,
          size: _pageSize,
        );
    }
  }

  /// Search transactions
  void searchTransactions(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// Set date filter
  void setDateFilter(DateTime? start, DateTime? end) {
    _startDate.value = start;
    _endDate.value = end;
    _applyFilters();
  }

  /// Set selected filter
  void setSelectedFilter(String filter) {
    _selectedFilter.value = filter;
    _applyFilters();
  }

  /// Apply all filters
  void _applyFilters() {
    List<TransactionModel> filtered = List.from(_transactions);

    // Apply search filter
    if (_searchQuery.value.isNotEmpty) {
      filtered = filtered.where((transaction) {
        final searchLower = _searchQuery.value.toLowerCase();
        return (transaction.firstName?.toLowerCase().contains(searchLower) ?? false) ||
               (transaction.secondName?.toLowerCase().contains(searchLower) ?? false) ||
               (transaction.phoneNumber?.toLowerCase().contains(searchLower) ?? false) ||
               (transaction.transactionRef?.toLowerCase().contains(searchLower) ?? false) ||
               (transaction.transactionCode?.toLowerCase().contains(searchLower) ?? false);
      }).toList();
    }

    // Apply date filter
    if (_startDate.value != null || _endDate.value != null) {
      filtered = _transactionService.filterByDateRange(
        transactions: filtered,
        startDate: _startDate.value,
        endDate: _endDate.value,
      );
    }

    _filteredTransactions.assignAll(filtered);
  }

  /// Reset state
  void _resetState() {
    _transactions.clear();
    _filteredTransactions.clear();
    _searchQuery.value = '';
    _selectedFilter.value = '';
    _startDate.value = null;
    _endDate.value = null;
    _currentPage.value = 0;
    _hasMoreData.value = true;
    _isLoading.value = false;
    _isLoadingMore.value = false;
  }

  /// Refresh transactions
  Future<void> refreshTransactions() async {
    await loadTransactions(refresh: true);
  }

  /// Get transaction count
  int get transactionCount => _filteredTransactions.length;

  /// Check if transactions are empty
  bool get isEmpty {
    if (_config.transactionType == TransactionType.kitty) {
      try {
        final kittyController = Get.find<KittyController>();
        return kittyController.transactionsKitty.isEmpty && !kittyController.loadingTransactions.value;
      } catch (e) {
        return _filteredTransactions.isEmpty && !_isLoading.value;
      }
    }
    return _filteredTransactions.isEmpty && !_isLoading.value;
  }

  @override
  void onClose() {
    _resetState();
    super.onClose();
  }
}
