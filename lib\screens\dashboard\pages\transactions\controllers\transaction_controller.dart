// Unified Transaction Controller
// Manages state and business logic for all transaction types

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import '../models/transaction_type.dart';
import '../models/transaction_filter_model.dart';
import '../services/transaction_service.dart';
import '../utils/filter_error_handler.dart';

class TransactionController extends GetxController {
  final TransactionService _transactionService = Get.find<TransactionService>();
  final Logger _logger = Get.find<Logger>();

  // Observable properties
  final RxList<TransactionModel> _transactions = <TransactionModel>[].obs;
  final RxList<TransactionModel> _filteredTransactions = <TransactionModel>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isLoadingMore = false.obs;
  final RxBool _isFilterLoading = false.obs;
  final Rx<TransactionFilterModel> _filterModel = const TransactionFilterModel().obs;
  final Rx<FilterError?> _lastError = Rx<FilterError?>(null);
  final RxInt _currentPage = 0.obs;
  final RxBool _hasMoreData = true.obs;

  // Text controllers for filter inputs
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController codeController = TextEditingController();
  final TextEditingController startDateController = TextEditingController();
  final TextEditingController endDateController = TextEditingController();

  // Configuration
  late TransactionPageConfig _config;
  final int _pageSize = 20;

  // Getters
  List<TransactionModel> get transactions => _transactions.toList();
  List<TransactionModel> get filteredTransactions => _filteredTransactions.toList();
  bool get isLoading => _isLoading.value || _isKittyLoading();
  bool get isLoadingMore => _isLoadingMore.value;
  bool get isFilterLoading => _isFilterLoading.value;
  TransactionFilterModel get filterModel => _filterModel.value;
  String get searchQuery => _filterModel.value.searchQuery;
  FilterType get selectedFilterType => _filterModel.value.selectedFilterType;
  String get phoneNumber => _filterModel.value.phoneNumber;
  String get transactionCode => _filterModel.value.transactionCode;
  DateTime? get startDate => _filterModel.value.startDate;
  DateTime? get endDate => _filterModel.value.endDate;
  bool get hasMoreData => _hasMoreData.value;
  bool get hasActiveFilters => _filterModel.value.hasActiveFilters;
  bool get hasAdvancedFilters => _filterModel.value.hasAdvancedFilters;
  FilterError? get lastError => _lastError.value;
  bool get hasError => _lastError.value != null;
  TransactionPageConfig get config => _config;

  // Check if kitty transactions are loading
  bool _isKittyLoading() {
    if (_config.transactionType == TransactionType.kitty) {
      try {
        final kittyController = Get.find<KittyController>();
        return kittyController.loadingTransactions.value;
      } catch (e) {
        return false;
      }
    }
    return false;
  }

  /// Initialize controller with configuration
  void initialize(TransactionPageConfig config) {
    _config = config;
    _resetState();
    loadTransactions();
  }

  /// Load transactions based on configuration
  Future<void> loadTransactions({bool refresh = false}) async {
    if (refresh) {
      _currentPage.value = 0;
      _hasMoreData.value = true;
      _transactions.clear();
    }

    if (_isLoading.value || (!_hasMoreData.value && !refresh)) return;

    _isLoading.value = true;

    try {
      List<TransactionModel> newTransactions = await _fetchTransactionsForType();
      
      if (refresh) {
        _transactions.assignAll(newTransactions);
      } else {
        _transactions.addAll(newTransactions);
      }

      _hasMoreData.value = newTransactions.length >= _pageSize;
      _currentPage.value++;
      
      _applyFilters();
    } catch (e) {
      _logger.e('Error loading transactions: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load more transactions (pagination)
  Future<void> loadMoreTransactions() async {
    if (_isLoadingMore.value || !_hasMoreData.value) return;

    _isLoadingMore.value = true;

    try {
      List<TransactionModel> newTransactions = await _fetchTransactionsForType();
      _transactions.addAll(newTransactions);
      
      _hasMoreData.value = newTransactions.length >= _pageSize;
      _currentPage.value++;
      
      _applyFilters();
    } catch (e) {
      _logger.e('Error loading more transactions: $e');
    } finally {
      _isLoadingMore.value = false;
    }
  }

  /// Fetch transactions based on type
  Future<List<TransactionModel>> _fetchTransactionsForType() async {
    switch (_config.transactionType) {
      case TransactionType.user:
        final userController = Get.find<UserKittyController>();
        final user = userController.getLocalUser();
        return await _transactionService.fetchTransactions(
          type: TransactionType.user,
          phoneNumber: user?.phoneNumber,
          page: _currentPage.value,
          size: _pageSize,
        );

      case TransactionType.kitty:
        // Use KittyController directly for kitty transactions
        final kittyController = Get.put(KittyController());

        _logger.i('Fetching kitty transactions for kittyId: ${_config.entityId}');

        // If entityId is null or 0, use already loaded transactions (for seeAlltranscScreen route)
        if (_config.entityId == null || _config.entityId == 0) {
          _logger.i('Using already loaded kitty transactions: ${kittyController.transactionsKitty.length}');
          return kittyController.transactionsKitty.toList();
        }

        // Otherwise, load transactions for the specific kitty
        await kittyController.getKittyContributions(
          kittyId: _config.entityId!,
          page: 0,
          size: 100, // Load more transactions at once
        );

        final transactions = kittyController.transactionsKitty.toList();
        _logger.i('Loaded ${transactions.length} kitty transactions');

        // Return all transactions (no pagination for kitty transactions)
        return transactions;

      case TransactionType.chama:
        return await _transactionService.fetchTransactions(
          type: TransactionType.chama,
          entityId: _config.entityId,
          accountNo: _config.accountNo,
          page: _currentPage.value,
          size: _pageSize,
        );

      case TransactionType.event:
        return await _transactionService.fetchTransactions(
          type: TransactionType.event,
          entityId: _config.entityId,
          page: _currentPage.value,
          size: _pageSize,
        );
    }
  }

  /// Search transactions
  void searchTransactions(String query) {
    _filterModel.value = _filterModel.value.copyWith(searchQuery: query);
    _applyFilters();
  }

  /// Set filter type
  void setFilterType(FilterType filterType) {
    _filterModel.value = _filterModel.value.copyWith(selectedFilterType: filterType);
  }

  /// Set phone number filter
  void setPhoneNumberFilter(String phoneNumber) {
    _clearError();
    _filterModel.value = _filterModel.value.copyWith(phoneNumber: phoneNumber);
    phoneController.text = phoneNumber;
    _applyAdvancedFilters();
  }

  /// Set transaction code filter
  void setTransactionCodeFilter(String code) {
    _clearError();
    _filterModel.value = _filterModel.value.copyWith(transactionCode: code);
    codeController.text = code;
    _applyAdvancedFilters();
  }

  /// Set date range filter
  void setDateRangeFilter(DateTime? startDate, DateTime? endDate) {
    _clearError();
    _filterModel.value = _filterModel.value.copyWith(
      startDate: startDate,
      endDate: endDate,
    );
    startDateController.text = startDate != null ? _filterModel.value.formattedStartDate : '';
    endDateController.text = endDate != null ? _filterModel.value.formattedEndDate : '';
    _applyAdvancedFilters();
  }

  /// Clear specific filter type
  void clearFilterType(FilterType filterType) {
    _filterModel.value = _filterModel.value.clearFilterType(filterType);
    _updateControllers();
    if (filterType == FilterType.none) {
      _applyFilters();
    } else {
      _applyAdvancedFilters();
    }
  }

  /// Clear all filters
  void clearAllFilters() {
    _filterModel.value = _filterModel.value.clearAll();
    _updateControllers();
    _applyFilters();
  }

  /// Apply all filters (client-side filtering for search)
  void _applyFilters() {
    List<TransactionModel> filtered = List.from(_transactions);

    // Apply search filter
    if (_filterModel.value.searchQuery.isNotEmpty) {
      filtered = filtered.where((transaction) {
        final searchLower = _filterModel.value.searchQuery.toLowerCase();
        return (transaction.firstName?.toLowerCase().contains(searchLower) ?? false) ||
               (transaction.secondName?.toLowerCase().contains(searchLower) ?? false) ||
               (transaction.phoneNumber?.toLowerCase().contains(searchLower) ?? false) ||
               (transaction.transactionRef?.toLowerCase().contains(searchLower) ?? false) ||
               (transaction.transactionCode?.toLowerCase().contains(searchLower) ?? false);
      }).toList();
    }

    // Apply date filter (client-side for already loaded transactions)
    if (_filterModel.value.startDate != null || _filterModel.value.endDate != null) {
      filtered = _transactionService.filterByDateRange(
        transactions: filtered,
        startDate: _filterModel.value.startDate,
        endDate: _filterModel.value.endDate,
      );
    }

    _filteredTransactions.assignAll(filtered);
  }

  /// Apply advanced filters (server-side filtering)
  Future<void> _applyAdvancedFilters() async {
    if (!_filterModel.value.hasAdvancedFilters) {
      _applyFilters();
      return;
    }

    // Validate filter parameters
    final validationErrors = FilterErrorHandler.validateFilterModel(_filterModel.value);
    if (validationErrors.isNotEmpty) {
      _setError(validationErrors.first);
      FilterErrorHandler.showValidationErrors(validationErrors);
      return;
    }

    _isFilterLoading.value = true;
    _clearError();

    try {
      await _fetchFilteredTransactions();
    } catch (e) {
      final error = FilterErrorHandler.handleException(e);
      _setError(error);
      FilterErrorHandler.showErrorSnackbar(error);
      _logger.e('Error applying advanced filters: $e');
    } finally {
      _isFilterLoading.value = false;
    }
  }

  /// Clear error state
  void _clearError() {
    _lastError.value = null;
  }

  /// Set error state
  void _setError(FilterError error) {
    _lastError.value = error;
  }

  /// Retry last failed operation
  void retryLastOperation() {
    if (_filterModel.value.hasAdvancedFilters) {
      _applyAdvancedFilters();
    } else {
      refreshTransactions();
    }
  }

  /// Fetch filtered transactions from server
  Future<void> _fetchFilteredTransactions() async {
    switch (_config.transactionType) {
      case TransactionType.kitty:
        await _fetchKittyFilteredTransactions();
        break;
      case TransactionType.user:
        await _fetchUserFilteredTransactions();
        break;
      case TransactionType.chama:
        await _fetchChamaFilteredTransactions();
        break;
      case TransactionType.event:
        await _fetchEventFilteredTransactions();
        break;
    }
  }

  /// Update text controllers with current filter values
  void _updateControllers() {
    phoneController.text = _filterModel.value.phoneNumber;
    codeController.text = _filterModel.value.transactionCode;
    startDateController.text = _filterModel.value.formattedStartDate;
    endDateController.text = _filterModel.value.formattedEndDate;
  }

  /// Fetch filtered kitty transactions
  Future<void> _fetchKittyFilteredTransactions() async {
    try {
      final kittyController = Get.find<KittyController>();
      await kittyController.getKittyFiltrContributions(
        kittyId: _config.entityId ?? 0,
        eventId: _config.transactionType == TransactionType.event ? _config.entityId : null,
        phoneNumber: _filterModel.value.phoneNumber.isNotEmpty ? _filterModel.value.phoneNumber : null,
        code: _filterModel.value.transactionCode.isNotEmpty ? _filterModel.value.transactionCode : null,
        startDate: _filterModel.value.formattedStartDate.isNotEmpty ? _filterModel.value.formattedStartDate : null,
        endDate: _filterModel.value.formattedEndDate.isNotEmpty ? _filterModel.value.formattedEndDate : null,
      );

      // Update transactions with filtered results
      _transactions.assignAll(kittyController.filtrtransactions);

      // Check if no results found
      if (kittyController.filtrtransactions.isEmpty) {
        _setError(FilterError.noData('No kitty transactions match your filter criteria'));
      }

      _applyFilters(); // Apply search filter on server results
    } catch (e) {
      final error = FilterErrorHandler.handleException(e);
      _setError(error);
      throw error;
    }
  }

  /// Fetch filtered user transactions
  Future<void> _fetchUserFilteredTransactions() async {
    try {
      final userController = Get.find<UserKittyController>();
      final user = userController.getLocalUser();

      if (user?.phoneNumber == null) {
        _setError(FilterError.validation('User phone number not available'));
        return;
      }

      await userController.getUserFiltrContributions(
        phoneNo: user!.phoneNumber!,
        code: _filterModel.value.transactionCode.isNotEmpty ? _filterModel.value.transactionCode : null,
        startDate: _filterModel.value.formattedStartDate.isNotEmpty ? _filterModel.value.formattedStartDate : null,
        endDate: _filterModel.value.formattedEndDate.isNotEmpty ? _filterModel.value.formattedEndDate : null,
        kittId: _config.entityId,
      );

      // Update transactions with filtered results
      _transactions.assignAll(userController.filtrtransactions);

      // Check if no results found
      if (userController.filtrtransactions.isEmpty) {
        _setError(FilterError.noData('No user transactions match your filter criteria'));
      }

      _applyFilters(); // Apply search filter on server results
    } catch (e) {
      final error = FilterErrorHandler.handleException(e);
      _setError(error);
      throw error;
    }
  }

  /// Fetch filtered chama transactions (placeholder for future implementation)
  Future<void> _fetchChamaFilteredTransactions() async {
    // TODO: Implement chama filtering when API is available
    _logger.w('Chama filtering not yet implemented');
  }

  /// Fetch filtered event transactions (placeholder for future implementation)
  Future<void> _fetchEventFilteredTransactions() async {
    // TODO: Implement event filtering when API is available
    _logger.w('Event filtering not yet implemented');
  }

  /// Reset state
  void _resetState() {
    _transactions.clear();
    _filteredTransactions.clear();
    _filterModel.value = const TransactionFilterModel();
    _lastError.value = null;
    _currentPage.value = 0;
    _hasMoreData.value = true;
    _isLoading.value = false;
    _isLoadingMore.value = false;
    _isFilterLoading.value = false;
    _updateControllers();
  }

  /// Refresh transactions
  Future<void> refreshTransactions() async {
    await loadTransactions(refresh: true);
  }

  /// Get transaction count
  int get transactionCount => _filteredTransactions.length;

  /// Check if transactions are empty
  bool get isEmpty {
    if (_config.transactionType == TransactionType.kitty) {
      try {
        final kittyController = Get.find<KittyController>();
        return kittyController.transactionsKitty.isEmpty && !kittyController.loadingTransactions.value;
      } catch (e) {
        return _filteredTransactions.isEmpty && !_isLoading.value;
      }
    }
    return _filteredTransactions.isEmpty && !_isLoading.value;
  }

  @override
  void onClose() {
    // Dispose text controllers
    phoneController.dispose();
    codeController.dispose();
    startDateController.dispose();
    endDateController.dispose();

    _resetState();
    super.onClose();
  }
}
