// Transaction Filter Test
// Basic test to verify filtering functionality works correctly

import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/transaction_model.dart';
import '../models/transaction_filter_model.dart';
import '../models/transaction_type.dart';
import '../controllers/transaction_controller.dart';
import '../services/transaction_service.dart';

void main() {
  group('Transaction Filter Tests', () {
    late TransactionController controller;
    late TransactionService service;

    setUpAll(() {
      // Initialize GetX dependencies
      Get.put<Logger>(Logger());
      Get.put<TransactionService>(TransactionService());
    });

    setUp(() {
      service = Get.find<TransactionService>();
      controller = TransactionController();
      
      // Initialize with test config
      controller.initialize(const TransactionPageConfig(
        transactionType: TransactionType.kitty,
        entityId: 1,
        isFullPage: true,
      ));
    });

    tearDown(() {
      controller.dispose();
    });

    group('TransactionFilterModel Tests', () {
      test('should create empty filter model', () {
        const filter = TransactionFilterModel();
        
        expect(filter.searchQuery, isEmpty);
        expect(filter.selectedFilterType, FilterType.none);
        expect(filter.phoneNumber, isEmpty);
        expect(filter.transactionCode, isEmpty);
        expect(filter.startDate, isNull);
        expect(filter.endDate, isNull);
        expect(filter.hasActiveFilters, isFalse);
        expect(filter.hasAdvancedFilters, isFalse);
      });

      test('should detect active filters', () {
        const filter = TransactionFilterModel(
          searchQuery: 'test',
          phoneNumber: '*********',
        );
        
        expect(filter.hasActiveFilters, isTrue);
        expect(filter.hasAdvancedFilters, isTrue);
      });

      test('should validate phone number correctly', () {
        // Valid phone numbers
        const validFilter = TransactionFilterModel(phoneNumber: '*********');
        expect(validFilter.validate().isValid, isTrue);

        // Invalid phone numbers
        const invalidFilter = TransactionFilterModel(phoneNumber: '123');
        expect(invalidFilter.validate().isValid, isFalse);
      });

      test('should validate date range correctly', () {
        final now = DateTime.now();
        final yesterday = now.subtract(const Duration(days: 1));
        final tomorrow = now.add(const Duration(days: 1));

        // Valid date range
        final validFilter = TransactionFilterModel(
          startDate: yesterday,
          endDate: now,
        );
        expect(validFilter.validate().isValid, isTrue);

        // Invalid date range (start after end)
        final invalidFilter = TransactionFilterModel(
          startDate: now,
          endDate: yesterday,
        );
        expect(invalidFilter.validate().isValid, isFalse);

        // Invalid date range (future end date)
        final futureFilter = TransactionFilterModel(
          startDate: now,
          endDate: tomorrow,
        );
        expect(futureFilter.validate().isValid, isFalse);
      });

      test('should clear filters correctly', () {
        final filter = TransactionFilterModel(
          searchQuery: 'test',
          phoneNumber: '*********',
          transactionCode: 'ABC123',
          startDate: DateTime.now().subtract(const Duration(days: 1)),
          endDate: DateTime.now(),
        );

        final clearedFilter = filter.clearAll();
        expect(clearedFilter.hasActiveFilters, isFalse);
      });

      test('should convert to API parameters correctly', () {
        final filter = TransactionFilterModel(
          searchQuery: 'test search',
          phoneNumber: '*********',
          transactionCode: 'ABC123',
          startDate: DateTime(2024, 1, 1),
          endDate: DateTime(2024, 1, 31),
        );

        final params = filter.toApiParams();
        expect(params['search'], 'test search');
        expect(params['phone_number'], '*********');
        expect(params['transaction_code'], 'ABC123');
        expect(params['start-date'], '2024-01-01');
        expect(params['end-date'], '2024-01-31');
      });
    });

    group('FilterType Tests', () {
      test('should convert from string correctly', () {
        expect(FilterType.fromString('Filter'), FilterType.none);
        expect(FilterType.fromString('Account No'), FilterType.phone);
        expect(FilterType.fromString('code'), FilterType.code);
        expect(FilterType.fromString('Date'), FilterType.date);
        expect(FilterType.fromString('invalid'), FilterType.none);
      });

      test('should have correct display names', () {
        expect(FilterType.none.displayName, 'Filter');
        expect(FilterType.phone.displayName, 'Account No');
        expect(FilterType.code.displayName, 'code');
        expect(FilterType.date.displayName, 'Date');
      });
    });

    group('TransactionService Filter Tests', () {
      test('should apply client-side search filter', () {
        final transactions = [
          TransactionModel(
            id: 1,
            firstName: 'John',
            secondName: 'Doe',
            phoneNumber: '*********',
            transactionCode: 'ABC123',
          ),
          TransactionModel(
            id: 2,
            firstName: 'Jane',
            secondName: 'Smith',
            phoneNumber: '*********',
            transactionCode: 'XYZ789',
          ),
        ];

        final filter = const TransactionFilterModel(searchQuery: 'john');
        final filtered = service.applyClientFilters(
          transactions: transactions,
          filterModel: filter,
        );

        expect(filtered.length, 1);
        expect(filtered.first.firstName, 'John');
      });

      test('should apply client-side phone filter', () {
        final transactions = [
          TransactionModel(
            id: 1,
            firstName: 'John',
            phoneNumber: '*********',
          ),
          TransactionModel(
            id: 2,
            firstName: 'Jane',
            phoneNumber: '*********',
          ),
        ];

        final filter = const TransactionFilterModel(phoneNumber: '712');
        final filtered = service.applyClientFilters(
          transactions: transactions,
          filterModel: filter,
        );

        expect(filtered.length, 1);
        expect(filtered.first.phoneNumber, '*********');
      });

      test('should apply client-side date range filter', () {
        final now = DateTime.now();
        final yesterday = now.subtract(const Duration(days: 1));
        final twoDaysAgo = now.subtract(const Duration(days: 2));

        final transactions = [
          TransactionModel(
            id: 1,
            createdAt: now,
          ),
          TransactionModel(
            id: 2,
            createdAt: twoDaysAgo,
          ),
        ];

        final filter = TransactionFilterModel(
          startDate: yesterday,
          endDate: now,
        );
        final filtered = service.applyClientFilters(
          transactions: transactions,
          filterModel: filter,
        );

        expect(filtered.length, 1);
        expect(filtered.first.id, 1);
      });
    });

    group('TransactionController Filter Tests', () {
      test('should update search query', () {
        controller.searchTransactions('test query');
        expect(controller.searchQuery, 'test query');
      });

      test('should update filter type', () {
        controller.setFilterType(FilterType.phone);
        expect(controller.selectedFilterType, FilterType.phone);
      });

      test('should update phone number filter', () {
        controller.setPhoneNumberFilter('*********');
        expect(controller.phoneNumber, '*********');
        expect(controller.phoneController.text, '*********');
      });

      test('should update transaction code filter', () {
        controller.setTransactionCodeFilter('ABC123');
        expect(controller.transactionCode, 'ABC123');
        expect(controller.codeController.text, 'ABC123');
      });

      test('should update date range filter', () {
        final startDate = DateTime(2024, 1, 1);
        final endDate = DateTime(2024, 1, 31);
        
        controller.setDateRangeFilter(startDate, endDate);
        expect(controller.startDate, startDate);
        expect(controller.endDate, endDate);
      });

      test('should clear all filters', () {
        // Set some filters
        controller.searchTransactions('test');
        controller.setPhoneNumberFilter('*********');
        controller.setTransactionCodeFilter('ABC123');
        
        expect(controller.hasActiveFilters, isTrue);
        
        // Clear all filters
        controller.clearAllFilters();
        expect(controller.hasActiveFilters, isFalse);
        expect(controller.searchQuery, isEmpty);
        expect(controller.phoneNumber, isEmpty);
        expect(controller.transactionCode, isEmpty);
      });
    });
  });
}
