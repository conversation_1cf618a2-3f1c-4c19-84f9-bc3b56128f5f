// Enhanced Transaction Filter Widget
// Main filter widget combining search and advanced filters with dropdown selector

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/utils/custom_drop_down.dart';
import '../../models/transaction_type.dart';
import '../../models/transaction_filter_model.dart';
import '../../controllers/transaction_controller.dart';
import 'phone_number_filter.dart';
import 'transaction_code_filter.dart';
import 'date_range_filter.dart';

class EnhancedTransactionFilter extends StatelessWidget {
  final TransactionType transactionType;
  final String? controllerTag;

  const EnhancedTransactionFilter({
    super.key,
    required this.transactionType,
    this.controllerTag,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<TransactionController>(tag: controllerTag ?? transactionType.name);
    
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          _buildSearchAndFilterRow(context, controller),
          SizedBox(height: 12.h),
          _buildConditionalFilterUI(context, controller),
        ],
      ),
    );
  }

  /// Build search bar and filter dropdown row
  Widget _buildSearchAndFilterRow(BuildContext context, TransactionController controller) {
    return Row(
      children: [
        // Search TextField
        Expanded(
          child: Obx(() => TextField(
            onChanged: controller.searchTransactions,
            decoration: InputDecoration(
              hintText: _getSearchHint(),
              hintStyle: TextStyle(
                color: Theme.of(context).hintColor,
                fontSize: 14.sp,
              ),
              filled: true,
              fillColor: Theme.of(context).scaffoldBackgroundColor,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(24.r),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(24.r),
                borderSide: BorderSide(
                  color: AppColors.primary,
                  width: 1.w,
                ),
              ),
              prefixIcon: Icon(
                Icons.search_rounded,
                color: Theme.of(context).hintColor,
                size: 24.h,
              ),
              suffixIcon: controller.searchQuery.isNotEmpty
                  ? IconButton(
                      icon: Icon(
                        Icons.clear_rounded,
                        color: Theme.of(context).hintColor,
                        size: 20.h,
                      ),
                      onPressed: () => controller.searchTransactions(''),
                    )
                  : null,
              contentPadding: EdgeInsets.symmetric(
                vertical: 12.h,
                horizontal: 16.w,
              ),
            ),
            style: TextStyle(
              fontSize: 14.sp,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          )),
        ),
        
        SizedBox(width: 12.w),
        
        // Filter Dropdown
        Obx(() => CustomDropDown(
          width: 150.w,
          hintText: "Filter",
          hintStyle: TextStyle(fontSize: 12.sp),
          items: _getFilterOptions(),
          prefix: Icon(
            Icons.filter_alt_rounded,
            size: 20.h,
            color: AppColors.primary,
          ),
          onChanged: (value) {
            final filterType = FilterType.fromString(value);
            controller.setFilterType(filterType);
          },
          fillColor: Theme.of(context).scaffoldBackgroundColor,
          borderDecoration: OutlineInputBoration(
            borderRadius: BorderRadius.circular(24.r),
            borderSide: BorderSide(
              color: AppColors.neutralGrey.withOpacity(0.3),
              width: 1.w,
            ),
          ),
        )),
      ],
    );
  }

  /// Build conditional filter UI based on selected filter type
  Widget _buildConditionalFilterUI(BuildContext context, TransactionController controller) {
    return Obx(() {
      final selectedFilter = controller.selectedFilterType;
      
      switch (selectedFilter) {
        case FilterType.phone:
          return PhoneNumberFilter(
            controller: controller,
            transactionType: transactionType,
          );
        case FilterType.code:
          return TransactionCodeFilter(
            controller: controller,
            transactionType: transactionType,
          );
        case FilterType.date:
          return DateRangeFilter(
            controller: controller,
            transactionType: transactionType,
          );
        case FilterType.none:
        return SizedBox(height: 3.h);
      }
    });
  }

  /// Get search hint text based on transaction type
  String _getSearchHint() {
    switch (transactionType) {
      case TransactionType.user:
        return "Search by name, phone, or reference...";
      case TransactionType.kitty:
        return "Search transactions...";
      case TransactionType.chama:
        return "Search chama transactions...";
      case TransactionType.event:
        return "Search event transactions...";
    }
  }

  /// Get filter options based on transaction type
  List<String> _getFilterOptions() {
    final baseOptions = [FilterType.none.displayName];
    
    switch (transactionType) {
      case TransactionType.user:
        return baseOptions + [
          FilterType.code.displayName,
          FilterType.date.displayName,
        ];
      case TransactionType.kitty:
        return baseOptions + [
          FilterType.phone.displayName,
          FilterType.code.displayName,
          FilterType.date.displayName,
        ];
      case TransactionType.chama:
        return baseOptions + [
          FilterType.phone.displayName,
          FilterType.code.displayName,
          FilterType.date.displayName,
        ];
      case TransactionType.event:
        return baseOptions + [
          FilterType.code.displayName,
          FilterType.date.displayName,
        ];
    }
  }
}

/// Custom input decoration for consistent styling
class OutlineInputBoration extends InputBorder {
  final BorderRadius borderRadius;
  final BorderSide borderSide;

  const OutlineInputBoration({
    required this.borderRadius,
    required this.borderSide,
  });

  @override
  InputBorder copyWith({BorderSide? borderSide}) {
    return OutlineInputBoration(
      borderRadius: borderRadius,
      borderSide: borderSide ?? this.borderSide,
    );
  }

  @override
  EdgeInsetsGeometry get dimensions => EdgeInsets.all(borderSide.width);

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    return Path()..addRRect(borderRadius.resolve(textDirection).toRRect(rect).deflate(borderSide.width));
  }

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    return Path()..addRRect(borderRadius.resolve(textDirection).toRRect(rect));
  }

  @override
  void paint(Canvas canvas, Rect rect, {double? gapStart, double gapExtent = 0.0, double gapPercentage = 0.0, TextDirection? textDirection}) {
    final Paint paint = borderSide.toPaint();
    final RRect outer = borderRadius.resolve(textDirection).toRRect(rect);
    canvas.drawRRect(outer, paint);
  }

  @override
  ShapeBorder scale(double t) {
    return OutlineInputBoration(
      borderRadius: borderRadius * t,
      borderSide: borderSide.scale(t),
    );
  }

  @override
  bool get isOutline => true;
}
